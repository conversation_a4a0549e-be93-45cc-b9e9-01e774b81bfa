name: CI
on:
  push:
  workflow_dispatch:
    inputs:
      MAVEN_ARGS:
        description: '<PERSON>ven args'
        required: false
        default: '-Dtest=x'
env:
  BROWSER: FIREFOX
  #BROWSER: CHROME
  #SELENIUM_VERSION_ARG: -Dselenium.version=4.0.0 # If want to override
  HOST_URL: http://localhost:8082/ecco-war
  TEST_RETRIES: 2
  # Multi-threaded builds currently dies in acceptance tests
  MVN_THREADS: -T1
  # Use MAVEN_ARGS: -Dtest={JavaClassName} to run just a single test
  # skip tests with MAVEN_ARGS: "-Dtest=x"
  MAVEN_OPTS: -Xmx1280M  -Dmaven.wagon.httpconnectionManager.ttlSeconds=25 -Dmaven.wagon.http.retryHandler.count=3
  NX_BRANCH: ${{github.event.number}}
  NX_RUN_GROUP: ${{github.run_id}}
jobs:
  parameters:
    name: Determine parameters
    runs-on: ubuntu-latest
    outputs:
      # from step 'build-and-test'
      build-and-test-name: ${{steps.build-and-test.outputs.name}}
      test-report-name: ${{steps.build-and-test.outputs.test-report-name}}
      acceptance-test-profiles: ${{steps.build-and-test.outputs.acceptance-test-profiles}}
      # from step 'deploy'
      deploy: ${{steps.deploy.outputs.deploy}}
      deploy-path: ${{steps.deploy.outputs.path}} # NB branch name
      deploy-matrix: ${{steps.deploy.outputs.matrix}}
      release-name: ${{steps.deploy.outputs.release-name}}
    steps:
      - name: Determine build and test parameters
        id: build-and-test
        # search ${skipITs}
        run: |
          ref="${{github.ref}}"
          echo "${{github.event.inputs.MAVEN_ARGS}}"
          if [[ "${ref}" = "refs/heads/ci-no-tests" ]]; then
            echo "name=No Tests" >> $GITHUB_OUTPUT
            echo "test-report-name=No Test Report" >> $GITHUB_OUTPUT
            echo "acceptance-test-profiles=-DskipITs -Dtest=x -Dskip.yarn" >> $GITHUB_OUTPUT
          elif [[ "${ref}" = "refs/heads/ci-int-tests" ]]; then
              echo "name=Integration Tests" >> $GITHUB_OUTPUT
              echo "test-report-name=Integration Test Report" >> $GITHUB_OUTPUT
              echo "acceptance-test-profiles=-Dskip.yarn" >> $GITHUB_OUTPUT
          else
            echo "name=API Tests" >> $GITHUB_OUTPUT
            echo "test-report-name=API Test Report" >> $GITHUB_OUTPUT
            echo "acceptance-test-profiles=-Penable-api-acceptance-tests -DskipITs" >> $GITHUB_OUTPUT
          fi
      - name: Determine deploy parameters
        id: deploy
        run: |
          ref="${{github.ref}}"
          date_now="$(date +'%Y%m%d')"
          echo "release-name=${ref#refs/heads/}-${date_now}-${{github.run_number}}" >> $GITHUB_OUTPUT
          if [[
            "${{github.event_name}}" != "push"
          && "${{github.event_name}}" != "workflow_dispatch"
           ]]; then
            echo "deploy=false" >> $GITHUB_OUTPUT
            echo "path=" >> $GITHUB_OUTPUT
            echo "matrix={}" >> $GITHUB_OUTPUT
          elif [[ "${ref}" = "refs/heads/main" ]]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "path=/" >> $GITHUB_OUTPUT
            echo 'matrix={"server": ["732939-www2.eccosolutions.co.uk", "742532-www3.eccosolutions.co.uk"], "artifact": ["ecco", "ecco-int-ql"]}' >> $GITHUB_OUTPUT
          elif [[
            "${ref}" = "refs/heads/test-branch"
            || "${ref}" = refs/heads/*-maint
            || "${ref}" = "refs/heads/nightly"
            || "${ref}" = "refs/heads/ci-no-tests"
          ]]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "path=/${ref#refs/heads/}" >> $GITHUB_OUTPUT
            echo 'matrix={"server": ["732939-www2.eccosolutions.co.uk", "742532-www3.eccosolutions.co.uk"], "artifact": ["ecco"], "include": [{"server": "732939-www2.eccosolutions.co.uk", "artifact": "ecco-int-ql"}]}' >> $GITHUB_OUTPUT
          else
            echo "deploy=false" >> $GITHUB_OUTPUT
            echo "path=" >> $GITHUB_OUTPUT
            echo "matrix={}" >> $GITHUB_OUTPUT
          fi
  log-build-parameters:
    name: Log build parameters
    runs-on: ubuntu-latest
    needs: parameters
    steps:
      - name: Log build parameters
        run: |
          echo "::stop-commands::yDvCQrxk"
          cat <<"EOF"
          ${{toJson(needs.parameters.outputs)}}
          EOF
          echo "::yDvCQrxk"
  build-and-test:
    name: ${{needs.parameters.outputs.build-and-test-name}}
    runs-on: ubuntu-latest
    needs: parameters
    permissions:
      contents: read
      issues: read
      checks: write
      pull-requests: write
    steps:
      - name: Checkout
        uses: actions/checkout@v2.3.3
        with:
          fetch-depth: 50
      # NB counting commits may be useful one day - less so here as we trigger on a commit. Usage: if: ${{ env.NEW_COMMIT_COUNT > 0 }}
      #- name: New commits
      #  run: echo "NEW_COMMIT_COUNT=$(git log --oneline --since '24 hours ago' | wc -l)" >> $GITHUB_ENV
      - name: Set up OpenJDK 17
        uses: actions/setup-java@v1.4.3
        with:
          java-version: 17
      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ${{env.HOME}}/.m2
          key: ${{runner.os}}-m2-${{hashFiles('**/pom.xml')}}
          restore-keys: ${{runner.os}}-m2-
      - name: Find yarn cache
        id: find-yarn-cache
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
      - name: Cache yarn dependencies
        uses: actions/cache@v4
        with:
          path: ${{steps.find-yarn-cache.outputs.dir}}
          key: ${{runner.os}}-yarn-${{hashFiles('**/yarn.lock')}}
          restore-keys: ${{runner.os}}-yarn-
      - name: Configure access token for GitHub Packages
        run: echo "//npm.pkg.github.com/:_authToken=${{secrets.PERSONAL_ACCESS_READ}}" > ~/.npmrc
      - name: Fetch main branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2020-12-31 origin main:main
      - name: Deepen this branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2020-12-31 origin ${{github.ref}}
      - name: mvn verify
        timeout-minutes: 120
        run: |
          xvfb-run --server-args="-ac -screen 0 1920x1080x24" --auto-servernum \
            mvn verify --batch-mode ${MVN_THREADS} -Dbrowser="${BROWSER}" \
            ${{needs.parameters.outputs.acceptance-test-profiles}} \
            -Dtests.retries="${TEST_RETRIES}" -Dspring.mail.host=smtp.mailtrap.io \
            -Dspring.mail.username=3de4c1c0de758c -Dspring.mail.password=6c0f8db0b5ddc8 \
            -Denv=dev -Ddb=h2 -Dliquibase=CREATE \
            -DfailIfNoTests=false  -Ddeploy.server=local ${SELENIUM_VERSION_ARG} \
            -Dtarget.host.url="${HOST_URL}" ${{github.event.inputs.MAVEN_ARGS}}
      - name: Report test results
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2.10.0
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          check_name: ${{needs.parameters.outputs.test-report-name}}
          files: "**/target/surefire-reports/TEST*.xml"
      - name: Upload ecco build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ecco
          path: ecco/target/*.war
          if-no-files-found: error
          retention-days: 1
      - name: Upload ecco-webapi build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ecco-webapi
          path: ecco-webapi-boot/target/*.jar
          if-no-files-found: error
          retention-days: 3 # failsafe, as this would still allow many releases to build up over 3 days
      - name: Upload ecco-int-ql build artifact
        uses: actions/upload-artifact@v4
        with:
          name: ecco-int-ql
          path: ecco-int-ql/target/*.jar
          if-no-files-found: error
          retention-days: 1
      - name: Upload Acceptance Tests logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco-acceptance-tests/logs/*
          if-no-files-found: ignore
      - name: Upload Acceptance Tests snapshots to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco-acceptance-tests/target/surefire-reports/*.png
          if-no-files-found: ignore
      - name: Upload Ecco logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco/logs/ecco.log
          if-no-files-found: ignore
      - name: Report build failure on Slack
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          author_name: ${{needs.parameters.outputs.build-and-test-name}}
          job_name: ${{needs.parameters.outputs.build-and-test-name}}
          status: ${{job.status}}
          fields: repo,job,ref,commit,message,author,took
        env:
          SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL}}
          MATRIX_CONTEXT: ${{toJson(matrix)}}
  release:
    name: Release
    runs-on: ubuntu-latest
    needs:
      - parameters
      - build-and-test
    # only release if we've determined we should push the code out
    if: success() && needs.parameters.outputs.deploy == 'true'
    #strategy:
    #    matrix: ${{fromJson(needs.parameters.outputs.deploy-matrix)}}
    steps:
      - name: Download ecco build artifact
        uses: actions/download-artifact@v4
        with:
          name: ecco # ie ecco/target/*.war, but could use ${{matrix.artifact}} if verify uploading multiple - see https://github.com/actions/upload-release-asset/issues/28
          #path: # we don't need to specify a path to place it as we just use the outputs.upload_url later
      - name: Download ecco-api build artifact
        uses: actions/download-artifact@v4
        with:
          name: ecco-webapi
      #- name: Zip
      #  uses: thedoctor0/zip-release@0.7.1
      #  with:
      #    filename: 'releases.zip'
      - name: Display structure of downloaded files
        run: ls -R
      - name: Create Release ${{needs.parameters.outputs.release-name}}
        id: create-new-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
          tag_name: ${{needs.parameters.outputs.release-name}}
          release_name: Release ${{needs.parameters.outputs.release-name}}
      - name: Upload ecco to GitHub Release run-${{github.run_number}} # Upload ${{matrix.artifact}} to ...
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
          upload_url: ${{steps.create-new-release.outputs.upload_url}}
          asset_path: ecco-war-1.0.0.CI-SNAPSHOT.war
          asset_name: ${{needs.parameters.outputs.release-name}}.war
          asset_content_type: application/java-archive
      - name: Upload ecco-webapi to GitHub Release run-${{github.run_number}} # Upload ${{matrix.artifact}} to ...
        uses: actions/upload-release-asset@v1
        env:
            GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
        with:
            upload_url: ${{steps.create-new-release.outputs.upload_url}}
            asset_path: ecco-webapi-boot-1.0.0.CI-SNAPSHOT.jar
            asset_name: ${{needs.parameters.outputs.release-name}}.jar
            asset_content_type: application/java-archive
#  deploy:
#    name: Deploy
#    runs-on: ubuntu-latest
#    needs:
#      - parameters
#      - build-and-test
#    if: success() && needs.parameters.outputs.deploy == 'true'
#    strategy:
#
#      matrix: ${{fromJson(needs.parameters.outputs.deploy-matrix)}}
#    steps:
#      - name: Download ${{matrix.artifact}} build artifact
#        uses: actions/download-artifact@v4
#        with:
#          name: ${{matrix.artifact}}
#      - name: Deploy ${{matrix.artifact}} to ${{matrix.server}}
#        uses: garygrossgarten/github-action-scp@v0.6.0
#        with:
#          # access through Rackspace firewall would involve updating firewall with many IPs
#          # IP Ranges https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#ip-addresses-of-github-hosted-runners
#          # instead we get a modern limited docker sshd on port 7011, see OPS-90
#          local: .
#          remote: /home/<USER>
#          host: ${{matrix.server}}
#          port: 7011
#          username: ${{secrets.DEPLOY_USER}}
#          privateKey: ${{secrets.DEPLOY_PRIVKEY}}
#          #password: ${{secrets.DEPLOY_PASS}}
#      - name: Report deploy failure on Slack
#        if: failure()
#        uses: 8398a7/action-slack@v3
#        with:
#          author_name: Deploy
#          job_name: Deploy
#          status: ${{job.status}}
#          fields: repo,job,ref,commit,message,author,took
#        env:
#          SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL}}
#          MATRIX_CONTEXT: ${{toJson(matrix)}}
#  upload-logs:
#    name: Upload logs of failed build and test
#    runs-on: ubuntu-latest
#    needs:
#      - build-and-test
#    if: failure() && needs.parameters.outputs.deploy == 'true'
#    steps:
#      - name: Download build logs
#        uses: actions/download-artifact@v4
#        with:
#          name: logs
#      - name: Upload logs
#        uses: garygrossgarten/github-action-scp@v0.6.0
#        with:
#          local: .
#          remote: /home/<USER>/github-actions-failure/
#          host: 732939-www2.eccosolutions.co.uk
#          port: 7011
#          username: ${{secrets.DEPLOY_USER}}
#          #password: ${{secrets.DEPLOY_PASS}}
#          privateKey: ${{secrets.DEPLOY_PRIVKEY}}
