package com.ecco.dom.groupsupport;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;


@Entity
@DiscriminatorValue("clientInvitation")
public class GroupActivityInvitationCommand extends GroupSupportCommand {
    public GroupActivityInvitationCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                          long userId, @Nonnull UUID activityUuid, @Nonnull String body) {
        super(uuid, remoteCreationTime, userId, activityUuid, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected GroupActivityInvitationCommand() {
    }
}
