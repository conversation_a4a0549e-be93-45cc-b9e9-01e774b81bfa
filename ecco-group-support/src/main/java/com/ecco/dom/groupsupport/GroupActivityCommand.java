package com.ecco.dom.groupsupport;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("groupsupportactivity")
public class GroupActivityCommand extends GroupSupportCommand {
    public GroupActivityCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                long userId, @Nonnull UUID activityUuid, @Nonnull String body) {
        super(uuid, remoteCreationTime, userId, activityUuid, body);
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected GroupActivityCommand() {
    }
}
