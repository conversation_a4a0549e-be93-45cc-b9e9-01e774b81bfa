package com.ecco.web.validators;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import com.ecco.web.form.ChangePasswordForm;

@Component
public class ChangePasswordFormValidator implements Validator {

    @Resource(name = "globalAnnotationValidator")
    private javax.validation.Validator javaxValidator;

    @Override
    public boolean supports(Class<?> clazz) {
        return ChangePasswordForm.class.isAssignableFrom(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        final ChangePasswordForm changePasswordForm = (ChangePasswordForm) target;
        if (StringUtils.isBlank(changePasswordForm.getOldPassword())) {
            errors.rejectValue("oldPassword", "oldPasswordRequired");
        }
        if (StringUtils.isBlank(changePasswordForm.getNewPassword())) {
            errors.rejectValue("newPassword", "newPasswordRequired");
        }
        if (StringUtils.isBlank(changePasswordForm.getConfirmNewPassword())) {
            errors.rejectValue("confirmNewPassword", "confirmNewPasswordRequired");
        }
        if (changePasswordForm.getNewPassword() != null
                && !changePasswordForm.getNewPassword().equals(changePasswordForm.getConfirmNewPassword())) {
            errors.rejectValue("confirmNewPassword", "newAndConfirmPasswordsDoNotMatch");
        }
        if (changePasswordForm.getNewPassword() != null && changePasswordForm.getOldPassword() != null
                && changePasswordForm.getNewPassword().equals(changePasswordForm.getOldPassword())) {
            errors.rejectValue("newPassword", "oldPasswordAndNewPasswordSame");
        }
    }

}
