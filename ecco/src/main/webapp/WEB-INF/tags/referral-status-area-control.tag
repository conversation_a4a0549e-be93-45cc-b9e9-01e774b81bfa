<%@tag body-content="empty" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>
<%@ taglib prefix="ecco" tagdir="/WEB-INF/tags" %>

<%@attribute name="serviceRecipientId" type="java.lang.Integer" required="true"%>
<%@attribute name="serviceType" type="com.ecco.serviceConfig.dom.ServiceType" required="true"%>
<%@attribute name="riskTaskName" type="java.lang.String" required="true"%>
<%@attribute name="riskUrl" type="java.lang.String" required="true"%>

<security:authorize access="hasAnyRole('ROLE_STAFF')">
    <script type="text/javascript">
    require(['jquery', 'evidence/risk/RiskStatusAreaControl'],
                function ($, RiskStatusAreaControl) {
            $(function() {
                var control = new RiskStatusAreaControl.RiskStatusAreaControl(${serviceRecipientId});
                control.attach($("#referralStatusArea"));
                // status area loaded by default
                control.load();
            });
        });
    </script>
    <%--
    <p>risk management required on:
        <c:if test="${outstandingThreats > 0}">
            <a href="${riskUrl}">
        </c:if>
        ${outstandingThreats}
        <c:if test="${outstandingThreats > 0}">
            </a>
        </c:if>
    </p>
    --%>
    <div id="referralStatusArea" data-risk-url="${riskUrl}">
    </div>
</security:authorize>
