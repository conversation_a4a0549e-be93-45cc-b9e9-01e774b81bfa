package com.ecco.webapp.config.test;

import com.azure.spring.aad.webapp.AADHandleConditionalAccessFilter;
import com.ecco.security.service.TotpService;
import com.ecco.test.support.UniqueDataService;
import com.ecco.testsupport.AbstractWebSecurityTest;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter;
import org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter;
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors;
import org.springframework.security.web.FilterChainProxy;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.access.intercept.AuthorizationFilter;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.AnonymousAuthenticationFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.switchuser.SwitchUserFilter;
import org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter;
import org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.context.SecurityContextPersistenceFilter;
import org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter;
import org.springframework.security.web.header.HeaderWriterFilter;
import org.springframework.security.web.savedrequest.RequestCacheAwareFilter;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter;
import org.springframework.security.web.session.DisableEncodeUrlFilter;
import org.springframework.security.web.session.SessionManagementFilter;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.web.filter.CorsFilter;

import javax.servlet.Filter;
import javax.servlet.http.HttpSession;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.InstanceOfAssertFactories.LIST;
import static org.assertj.core.util.introspection.FieldSupport.EXTRACTION;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestBuilders.formLogin;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class MfaWebSecurityTest extends AbstractWebSecurityTest {

    private final TotpService totpService = new TotpService();

    private final UniqueDataService uniqueDataService = UniqueDataService.instance;

    @Test
    public void loginPageAccessible() throws Exception {
        mockMvc.perform(get("/somethingwith/welcome/init"))
                .andDo(print())
                .andExpect(status().is(302))
                // with logs on we get 'Match found! Executing org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint@43a0015b'
                // and DefaultRedirectStrategy 'Redirecting to http://localhost/oauth2/authorization/azure'
                .andExpect(redirectedUrl("http://localhost/nav/secure/login.html"))
        ;
    }

    /**
     * By observation, we found the auth providers were different between tomcat/boot:
     * create a login, enable mfa, then login (using spring-boot) and we get 403 No Authentication Provider for the /api qr request
     *  - mfaPassthruAuthenticationProvider is MISSED in boot when logging in with qr enabled, but not on tomcat
     *  - breakpoints on
     *      - InitializeUserDetailsBeanManagerConfigurer configure
     *      - AuthenticationManagerBuilder authenticationProvider
     *      - ProviderManager if (provider.supports(toTest)) {
     *  - due to LegacyConfig, tomcat had customSpringJdbcUserDetailsManager but spring-boot didn't
     *      this means InitializeUserDetailsBeanManagerConfigurer getBeanOrNull has length != 1 (its 2!)
     *          - (customSpringJdbcUserDetailsManager and userManagementService)
     *      and so InitializeUserDetailsBeanManagerConfigurer
     *          - returns null for userDetailsService under tomcat
     *          - returns a new DaoAuthenticationProvider under spring-boot
     * which is where the first difference lies in the startup config
     * which was enough to get the qr code rendering (by adding customSpringJdbcUserDetailsManager to spring-boot)
     * so we test the auth providers here - because we don't include the LegacyConfig here we mimic spring-boot
     */
    @Test
    public void authProvidersFromTomcat() {
        String[] userDetails = this.context.getBeanNamesForType(UserDetailsService.class);
        // 0 = "userManagementService"
        // 1 = "customSpringJdbcUserDetailsManager"
        assertThat(userDetails.length).isEqualTo(2);

        String[] authDaoProviders = context.getBeanNamesForType(DaoAuthenticationProvider.class);
        // eccoAuthenticationProvider
        assertThat(authDaoProviders.length).isEqualTo(1);

        String[] authProviders = context.getBeanNamesForType(AuthenticationProvider.class);
        // 0 = "eccoAuthenticationProvider"
        // 1 = "mfaPassthruAuthenticationProvider"
        assertThat(authProviders.length).isEqualTo(2);
    }

    // copied from origin/main 10 May at 17bb12c
    // passes
    @Test
    public void filtersFromApplicationTest() {

        // If @EnableWebSecurity(debug = true) then we need to extract filter chain
        var chain = springSecurityFilterChain instanceof FilterChainProxy
                ? ((FilterChainProxy) springSecurityFilterChain)
                : EXTRACTION.fieldValue("filterChainProxy", FilterChainProxy.class, springSecurityFilterChain);

        var filters = chain.getFilters("/login");

        // Validate that the filter that handles /oauth2/authorization/azure is present
        Assertions.assertThat(filters)
                .extracting(Filter::getClass)
                .asInstanceOf(LIST)
                .containsSubsequence(
                        WebAsyncManagerIntegrationFilter.class,
                        SecurityContextPersistenceFilter.class,
                        HeaderWriterFilter.class,
                        CorsFilter.class,
                        LogoutFilter.class,
                        OAuth2AuthorizationRequestRedirectFilter.class,
                        AADHandleConditionalAccessFilter.class, // This is for client
                        OAuth2LoginAuthenticationFilter.class,
                        UsernamePasswordAuthenticationFilter.class,
                        DefaultLoginPageGeneratingFilter.class,
                        DefaultLogoutPageGeneratingFilter.class,
                        RequestCacheAwareFilter.class,
                        SecurityContextHolderAwareRequestFilter.class,
                        AnonymousAuthenticationFilter.class,
                        SessionManagementFilter.class,
                        ExceptionTranslationFilter.class,
//                        FilterSecurityInterceptor.class, // We don't have this... why
                        SwitchUserFilter.class,
                        AuthorizationFilter.class
                );

        // see also ApplicationTests contextContainsExpectedAutoConfiguredBeansAndFilters (spring-boot)
        var apiFilters = chain.getFilters("/api");
        Assertions.assertThat(apiFilters)
                .extracting(Filter::getClass)
                .asInstanceOf(LIST)
                .containsSubsequence(
                        DisableEncodeUrlFilter.class,
                        WebAsyncManagerIntegrationFilter.class,
                        SecurityContextPersistenceFilter.class,
                        HeaderWriterFilter.class,
                        CorsFilter.class,
                        LogoutFilter.class,
                        BasicAuthenticationFilter.class,
                        RequestCacheAwareFilter.class,
                        SecurityContextHolderAwareRequestFilter.class,
                        AnonymousAuthenticationFilter.class,
                        SessionManagementFilter.class,
                        ExceptionTranslationFilter.class,
                        FilterSecurityInterceptor.class
                );
    }

    @Test
    public void mfaWhenAllFactorsThenSucceeds() throws Exception {
        var username = uniqueDataService.userNameFor("user");

        String mfaSecret = totpService.generateKey();
        createUser(username, "userpwd", true, mfaSecret, uniqueDataService.idFor("STAFF"));

        MvcResult result = this.mockMvc.perform(formLogin()
                .loginProcessingUrl("/nav/secure/j_acegi_security_check")
                .userParameter("j_username")
                .passwordParam("j_password")
                .user(username)
                .password("userpwd"))
            .andExpect(redirectedUrl("/nav/mfa/validate"))
            .andReturn();

        HttpSession session = result.getRequest().getSession();
        //var p = result.getRequest().getUserPrincipal();
        //var auth = SecurityContextHolder.getContext().getAuthentication();
        //assertThat(auth, instanceOf(MfaAuthentication.class));
        assert session != null;

        // check we aren't actually logged in
        mockMvc.perform(get("/nav/r/welcome/"))
                .andDo(print())
                .andExpect(status().is(302))
                .andExpect(redirectedUrl("http://localhost/nav/secure/login.html"))
        ;

        // check we can mfa in
        var totp = totpService.getExpectedTotp(mfaSecret);
        this.mockMvc.perform(post("/nav/mfa/validate")
                .session((MockHttpSession) session)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("totp", totp)
                .with(SecurityMockMvcRequestPostProcessors.csrf()))
                .andDo(print())
            .andExpect(redirectedUrl("/nav/r/welcome/"));
    }


    @Test
    public void mfaWhenFormWrongThenFails() throws Exception {
        String mfaSecret = totpService.generateKey();
        var username = uniqueDataService.userNameFor("userfails2");
        createUser(username, "userpwd", true, mfaSecret, uniqueDataService.idFor("STAFF"));

        this.mockMvc.perform(formLogin()
                        .loginProcessingUrl("/nav/secure/j_acegi_security_check")
                        .userParameter("j_username")
                        .passwordParam("j_password")
                        .user(username)
                        .password("userpwdINCORRECT"))
                .andExpect(redirectedUrl("/nav/secure/login.html?login_error=1"))
                .andReturn();
    }

    @Test
    public void mfaWhenMfaWrongThenFails() throws Exception {
        String mfaSecret = totpService.generateKey();
        var username = uniqueDataService.userNameFor("userfails3");
        createUser(username, "userpwd", true, mfaSecret, uniqueDataService.idFor("STAFF"));

        var result = this.mockMvc.perform(formLogin()
                        .loginProcessingUrl("/nav/secure/j_acegi_security_check")
                        .userParameter("j_username")
                        .passwordParam("j_password")
                        .user(username)
                        .password("userpwd"))
                .andExpect(redirectedUrl("/nav/mfa/validate"))
                .andReturn();

        HttpSession session = result.getRequest().getSession();

        // fake totp
        this.mockMvc.perform(post("/nav/mfa/validate")
                        .session((MockHttpSession) session)
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                        .param("totp", "fake-totp")
                        .with(SecurityMockMvcRequestPostProcessors.csrf()))
                .andDo(print())
                .andExpect(redirectedUrl("/nav/mfa/validate"))
                .andExpect(flash().attribute("invalid", "totp"));

        // check we aren't actually logged in
        mockMvc.perform(get("/nav/r/welcome/"))
                .andDo(print())
                .andExpect(status().is(302))
                .andExpect(redirectedUrl("http://localhost/nav/secure/login.html"));
    }

}
