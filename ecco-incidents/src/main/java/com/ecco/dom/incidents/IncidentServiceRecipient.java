package com.ecco.dom.incidents;

import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.persistence.*;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.Individual;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.dom.contacts.AddressLike;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Entity
@Getter
@Setter
@DiscriminatorValue(IncidentServiceRecipient.DISCRIMINATOR)
public class IncidentServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "inc";
    public static final String PREFIX = "i"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts
    private static final DateTimeFormatter format = DateTimeFormatter.ofLocalizedDate(FormatStyle.MEDIUM);

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade= CascadeType.REMOVE)
    private Incident incident;

    @OneToMany(mappedBy = "serviceRecipient", orphanRemoval = true, cascade = CascadeType.REMOVE, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @OrderBy("id DESC")
    private Set<ServiceRecipientAttachment> attachments = new HashSet<>(); // see https://hibernate.atlassian.net/browse/HHH-9940

    @Override
    public String getDisplayName() {
        return incident.getReceivedDate() != null ? format.format(incident.getReceivedDateJdk()) : "";
    }

    @Override
    public AddressLike getAddress() {
        return null;
    }

    @Override
    public String getCalendarId() {
        return null;
    }

    @Override
    public Individual getContact() {
        return null;
    }

    @Override
    public Map<String,String> getTextMap() {
        return null;
    }

    @Override
    public String getParentCode() {
        return incident.getId().toString();
    }

    @Override
    public Long getParentId() {
        return null;
    }

    @Override
    public Incident getTargetEntity() {
        return incident;
    }

    @Override
    public String getPrefix() {
        return PREFIX;
    }
}
