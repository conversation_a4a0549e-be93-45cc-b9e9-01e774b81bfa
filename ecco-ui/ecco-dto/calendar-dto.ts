// import {EventObject} from "@fullcalendar/core" //doesn't exist. It was from old fullcalendar.d.ts and it differs
import {Encrypted, HateoasResource} from "@eccosolutions/ecco-common";
import {Address, Individual} from "./contact-dto";
import {LocationDto} from "./evidence-dto";
import {DemandScheduleDto} from "./rota-schedule-dto";

/** @see Attendee.java */
export enum AttendeeStatus {
    // noinspection JSUnusedGlobalSymbols (ACCEPTED)
    TENTATIVE,
    ACCEPTED,
    DECLINED
}

/**
 * Matches java.util.Calendar
 */
export enum CalendarDays {
    SUN = 1,
    MON,
    TUE,
    WED,
    THUR,
    FRI,
    SAT
}

export type ShortDayName = "Sun" | "Mon" | "Tues" | "Wed" | "Thur" | "Fri" | "Sat"

export const CalendarDayNames: (ShortDayName|null)[] = [null, "Sun", "Mon", "<PERSON><PERSON>", "Wed", "Thur", "Fri", "Sat"];

export const daysOrdered = [CalendarDays.MON, CalendarDays.TUE, CalendarDays.WED, CalendarDays.THUR, CalendarDays.FRI, CalendarDays.SAT, CalendarDays.SUN];

export function javaDayToString(day: CalendarDays): ShortDayName {
    return CalendarDayNames[day]!;
}

export function calendarDaysToMap(days: CalendarDays[]): {[day in ShortDayName]: boolean} {
    const result = {} as {[day in ShortDayName]: boolean};
    daysOrdered.forEach(day => {
        result[javaDayToString(day)] = days.indexOf(day) >= 0;
    });
    return result; // Miraculously ordered when processed using Object.entries()
}

/** Turn those that are present into a map */
export function calendarDaysAsMap(days: CalendarDays[]): {[day in ShortDayName]: boolean} {
    const result = {} as {[day in ShortDayName]: boolean};
    days.forEach(day => {
        result[javaDayToString(day)] = days.indexOf(day) >= 0;
    });
    return result; // Miraculously ordered when processed using Object.entries()
}

export function daysMapToJavaDayArray(days: {[day in ShortDayName]: boolean}) {
    const result = [] as CalendarDays[];
    CalendarDayNames.forEach((key, index) => {
        if (key != null && days[key]) {
            result.push(index);
        }
    });
    return result;
}

// .map(day => daysOrdered.indexOf(day) + 2).map(dayI => dayI == 8 ? 1 : dayI)

/** @see EventAttendee.java */
export interface EventAttendee {
    name: string;
    email: string | null;
    required: boolean;
    status: string | null; // was AttendeeStatus;

    /**
     * Unique reference to the native object of the user (calendarId) of the attendee.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    calendarIdUserReferenceUri: string; // cosmo representation - see CosmoHelper.syncAttendeesWithCalendars

    calendarId: string; // ecco representation (pointing to the user's collection)
}

// Our own copy of the old FullCalendar.EventObject from fullcalendar.d.ts 2.7.x which we used in our API
// This can be merged into EventResourceSecretFields potentially, or could stand alone as an interface
export interface EventObject {
    title: string;

    /** The start time of the interval during which this event/activity will take place.
     *  ISO8601 local-datetime */
    start: string;
    /** The end time of the interval during which this event/activity will take place.
     *  ISO8601 local-datetime */
    end: string;
    /** Whether the appointment is allDay */
    allDay: boolean;

    // NB to repeatEveryWeeks and repeatEndDate / scheduleEndDate we need to pull the info from NoteItem
    // by obtaining the stamp.getRecurrence... and see setItemEventStamp
}

/** Matches CalendarableEventDto.kt */
export interface CalendarableEventDto extends EventObject {

    /** The address of the event */
    location?: string;
}

/** @see EventResource.java */
export interface EventResourceSecretFields extends CalendarableEventDto {
    /** Whether this event is a recurrence, so is part of a recurring series */
    recurrence: boolean;

    /** Indication that this event is itself setting the schedule for recurrences. */
    recurringEntry: boolean;

    /** @Deprecated This needs to be controlled client-side */
    classNames: string[];

    attendees?: EventAttendee[];

    /** EventType.java */
    eventType: string;

    /** user category of the user event - a list definition */
    eventCategoryId: number;

    eventStatusId: number;

    /** Rate id as recorded with work evidence or when validating work (listdef id) */
    eventStatusRateId: number;

    /** Optional evidence item related to this event. */
    evidenceWorkUuid: string;
}

export interface EventResourcePlainFields extends HateoasResource {
    /** Event id for the calendaring system in use (see CustomEventImpl#uid which maps to CAL_UID)
     *  Format is {uuid[':'timestampIso8601]} */
    uid: string;

    /**
     * Unique reference to the native object of the user (calendarId) who created the event.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    calendarIdUserReferenceUri?: string;

    /**
     * The owner's calendarId - complements AttendeeAdapter#attendee.
     * This is the calendarId of calendarIdUserReferenceUri.
     */
    ownerCalendarId: string;

    /**
     * The service recipient this event is about - if any.
     * This is simply a convenience to create a change entry command - which keeps audits unified.
     * NB This is not the same as the author - a user should be able to create an event on another's calendar.
     */
    serviceRecipientId?: number;
    serviceRecipientPrefix?: string;
    serviceAllocationId?: number;

    /**
     * The contact this event is about - if any.
     * This is simply a convenience to create a change entry command - which keeps audits unified.
     * NB This is not the same as the author - a user should be able to create an event on another's calendar.
     */
    contactId?: number;

    /** Added for debugging purposes to see which rota schedules need extending. */
    updatedByUri?: string;
}

/** @see EventResource.java */
export type EventResourceDto = EventResourcePlainFields & EventResourceSecretFields;

export interface EventResourceDtoWithUserCalendar extends EventResourceDto {
    calendarIdUserReferenceUri: string;
    /**
     * The calendarId that this event was fetched via. This helped us to know which name to display.
     * However, we now are able to just specify 'not me' (or 'not x' if viewing via user x)
     * CLIENT-SIDE ONLY: This is added in careSingleVisitDataLoader
     */
    requestedCalendarId?: string;
}

export type SecureEventResource = Encrypted<EventResourceSecretFields, EventResourcePlainFields>;

/** See CalendarEventSnapshotDto */
export interface EventSnapshotDto {
    /** The eventUid (UUID or UUID:YYYYMMDDThhmmss[Z]) that the work is for (eg 'rota visit') */
    eventUid: string;

    demandScheduleId: number;

    demandScheduleDto: DemandScheduleDto;

    /** srId that the contact was associated with (ie visiting) */
    serviceRecipientId: number;

    /** service recipient assigned service */
    serviceAllocationId: number;

    /** The demand of the event (ie the client) */
    demandContact: Individual;

    /** The expected resource to attend and record the event (ie the contact) */
    resourceContact: Individual;

    /** The expected time the event starts (ISO datetime with UTC zone) */
    plannedStartInstant: string;

    /** The expected time the event ends (ISO datetime with UTC zone) */
    plannedEndInstant: string;

    plannedLocation?: Address;

    /** The actual location of the contact at this (created) moment in time -
     * according to the last command received */
    location?: LocationDto;

    /** The actual start time of the event (ISO datetime with UTC zone) */
    startInstant?: string;

    /** The actual end time of the event (ISO datetime with UTC zone) */
    endInstant?: string;

    /** The actual author of the work */
    contactId?: number;

    /** The actual evidence associated */
    workUuid?: string;
}