import {bus, EventBus} from "@eccosolutions/ecco-common";
import {BaseServiceRecipientCommandDto} from "./evidence-dto";
import {TaskEvidenceType} from "./service-config-dto";

/**
 * EvidenceUpdateEvent + GoalUpdateEvent
 *
 * Dynamically add a new smart step (from AddGoalForm, or '+')
 * For graph / tabular / GoalsControl / checks:
 *      Triggered from AddGoalForm (from BaseEvidenceForm) using GoalUpdateEvent and EvidenceUpdateEvent (which has a bus per evidenceGroup)
 *          - 'addGoal' fires from the graph approach (after its saved) and from tabular showAddGoalForm (which doesn't save)
 *      Handled (GoalUpdateEvent) by BaseEvidenceControl updateAction for graph and tabular
 *      Handled (EvidenceUpdateEvent) by MultiInstanceListControl (eg Snapshot/Command) calling MultiActionsControl.applyCommand
 *
 *      (NB GoalsControl also implements an independent in-situ version)
 *      (NB the '+' ui event from MultiActionsControl.createNewL1 also creates events GoalUpdateCommand and EvidenceUpdateAction)
 *      (NB 'checks' use ChecksEvidenceForm - from BuildingOverviewControl- which extends BaseEvidenceForm
 *          but handled in MultiInstanceListControl - from ChecksEvidenceForm)
 *
 * For checklist:
 *      Triggered from ChecklistEvidenceForm which creates a GoalUpdateEvent itself (as it does not extend BaseEvidenceForm)
 *      Handled by CheckInstanceForm via BaseActionInstanceControl
 */

export class EvidenceUpdateEvent {
    private static buses: {[evidenceGroup: string]: EventBus<EvidenceUpdateEvent>} = {};

    public static bus(evidenceGroupName: string) {
        if (!EvidenceUpdateEvent.buses[evidenceGroupName]) {
            console.log("Creating EvidenceUpdateEvent bus: " + evidenceGroupName);
            EvidenceUpdateEvent.buses[evidenceGroupName] = bus();
        }
        return EvidenceUpdateEvent.buses[evidenceGroupName];
    }

    /** TODO: commandDto is not a perfect match... we'll see how things go and refactor */
    constructor(
        public commandDto: BaseServiceRecipientCommandDto,
        public uniqueContextUuid?: string
    ) {}
}

/**
 * Allow newer code to hook up older code - history button on newer evidence pages
 */
export class HistoryLinkEvent {
    public static bus = bus<HistoryLinkEvent>();

    constructor(public srId: number, public taskName: string, public type: TaskEvidenceType) {}
}

/**
 * Allow newer code to hook up older code - button to open newer evidence pages
 */
export class EvidenceLinkEvent {
    public static bus = bus<EvidenceLinkEvent>();

    constructor(public srId: number, public taskName: string) {}
}

/**
 * Event emitted by a component saying where it would like a configured control (currently old HactNotificationControl)
 * to appear when relevant.
 * This is done this way to allow the older code HactNotificationControl to appear on newer evidence pages
 */
export class HactControlEvent {
    public static bus = bus<HactControlEvent>();

    constructor(public srId: number, public clientId: number, public elm: HTMLDivElement) {}
}

/**
 * An event to trigger an already registered
 */
export class HactCheckEvent {
    public static bus = bus<HactCheckEvent>();

    constructor() {}
}

export class QuestionAnswerTransientEvent {
    public static bus = bus<QuestionAnswerTransientEvent>();

    /**
     * @param questionDefId
     * @param valueTo null indicates a reset to the initial answer
     */
    constructor(public questionDefId: number, public valueTo: string | null) {}
}
