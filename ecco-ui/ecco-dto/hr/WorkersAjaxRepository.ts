///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import {EccoDate, Result, StringToObjectMap} from "@eccosolutions/ecco-common";
import {PersonSearchCriteriaDto} from "../client-dto";
import {PersonUserSummary} from "../contact-dto";
import {DelegateResponse} from "../dto";
import {StaffDto, StaffJobDto} from "../hr-dto";
import {SessionData} from "../session-data/feature-config-domain";
import {WorkersRepository} from "./WorkersRepository";
import {ApiClient} from "../web-api";
import {sequentialMapAll} from "./invokePromises";

function processLinear<T, R>(keys: T[], singlePromise: (id: T) => Promise<R[]>) {
    return sequentialMapAll(keys, singlePromise).then(data => {
        return data && data.reduce((r, x) => r.concat(x), []); // flatMap
    });
}

/** Talks to WorkerController */
export class WorkersAjaxRepository implements WorkersRepository {
    constructor(private apiClient: ApiClient) {}

    public findOneWorkerJob(workerJobId: number): Promise<StaffJobDto> {
        return this.apiClient.get<StaffJobDto>(`workerJob/${workerJobId.toString()}/`);
    }
    public static createWorkerJobIdExtractor(result: Result): number {
        // see comments on WorkerJobController.EXTRACT_ID_FN
        const href = result.links!![0].href;
        const matches = /workerJob\/(\d+)\//.exec(href);
        return Number(matches!![1]);
    }

    public findOneWorker(workerId: number): Promise<StaffDto> {
        return this.apiClient.get<StaffDto>(`workers/${workerId.toString()}/`);
    }

    public findWorkersByServiceRecipientIds(srIds: number[] | string[]): Promise<StaffDto[]> {
        return this.apiClient.get<StaffDto[]>("workers/byServiceRecipientIds/", {
            query: {
                ids: srIds.join(",")
            }
        });
    }

    /**
     * Return the worker where the jobs are those available at date.
     *
     * NB See ServiceCatWorkerRotaHandler, because its likely we are
     * doing the same operation - one from rota server-side, one from scheduler client-side.
     *       var contactIds = usersWithAccessTo.stream().map(IndividualUserSummary::getIndividualId).toList();
     *       var workerJobs = workerJobRepository.findAllStaffWithIndividualIdEmployedAt(contactIds, startDate);
     * @param individualIds typically from the result of calling users with access to - to limit the staff returned
     * @param date employed at
     */
    public findWorkersEmployedAtByIndividualIds(
        individualIds: number[],
        employedAt: EccoDate
    ): Promise<StaffDto[]> {
        return this.apiClient.get<StaffDto[]>(
            `workers/employedAt/${employedAt.formatIso8601()}/byContactIds/`,
            {
                query: {
                    ids: individualIds.join(",")
                }
            }
        );
    }

    public findAllWorkers(): Promise<StaffDto[]> {
        return this.apiClient.get<StaffDto[]>("workers/");
    }

    public findAllWorkersAtBuilding(buildingId: number): Promise<StaffDto[]> {
        return this.apiClient.get<StaffDto[]>(`buildings/${buildingId}/workers/`);
    }

    findAllStaffByCriteria(
        criteria: PersonSearchCriteriaDto,
        existingOnly = false
    ): Promise<StringToObjectMap<DelegateResponse<StaffDto[]>>> {
        return this.apiClient.post<StringToObjectMap<DelegateResponse<StaffDto[]>>>(
            `staff/${existingOnly ? "local" : "all"}/query`,
            criteria
        );
    }

    /**
     * IdName returned is the individualId and name of the staff, see AclListsController.toViewModel
     * This does not depend on ACL's being enabled.
     */
    public findWorkersWithAccessTo(
        serviceId: number,
        projectId?: number,
        role = "ROLE_STAFF"
    ): Promise<PersonUserSummary[]> {
        const query = {
            serviceId: serviceId.toString(),
            role,
            projectId: projectId?.toString()
        };
        return this.apiClient.get<PersonUserSummary[]>("usersWithAccessTo/", {query});
    }

    public findWorkersWithSameAccess(
        sessionData: SessionData,
        excludeMe = false,
        role = "ROLE_STAFF"
    ): Promise<PersonUserSummary[]> {
        // contactNames from CalendarViewController could be replaced client-side...
        // /usersWithAccessTo/ is cachedModerately (see AclListsController)
        let access: {serviceId: number; projectId: number | undefined}[];
        if (sessionData.isEnabled("referral.list.service-group")) {
            access = sessionData.getRestrictedServiceCategorisations(false).map(srvCat => {
                return {serviceId: srvCat.serviceId, projectId: srvCat.projectId};
            });
        } else {
            access = sessionData.getRestrictedServices().map(srv => {
                return {serviceId: srv.id, projectId: undefined};
            });
        }

        const accessQ = (allAccess: {serviceId: number; projectId: number | undefined}[]) => {
            const singlePromise = (a: {serviceId: number; projectId: number | undefined}) =>
                this.findWorkersWithAccessTo(a.serviceId, a.projectId, role);
            return processLinear(allAccess, singlePromise);
        };

        return accessQ(access).then((workers: PersonUserSummary[]) => {
            // double check it is unique
            const workersUniqId = [...new Set(workers.map(w => w.id))];
            const workersUniq = workersUniqId.map(id => workers.find(w => w.id == id)!);
            // excludeMe
            return excludeMe
                ? workersUniq.filter(
                      w => w.id != sessionData.getDto().individualUserSummary.individualId
                  )
                : workersUniq;
        });
    }

    saveStaff(staff: StaffDto): Promise<Result> {
        return this.apiClient.post<Result>("workers/", staff);
    }

    // TODO Its possible now to go back to using serviceCategorisations directly to get the services
    // TODO (using restrictedServicesProjects was required as serviceCategorisations assumed project security was assigned)
    private getRestrictedServices(sessionData: SessionData): number[] {
        // SessionData provides servicesProjects as well as serviceCategorisations
        // we need servicesProjects since they allow null projects, which the serviceCategorisations specifically avoid
        return [...new Set(sessionData.getRestrictedServices().map(srv => srv.id))];
    }

    public linkUser(workerId: number, username: string): Promise<Result> {
        return this.apiClient.post<Result>(`workers/${workerId}/linkUser/${username}`, {});
    }
}
