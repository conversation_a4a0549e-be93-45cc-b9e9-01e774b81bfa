import {EccoDate, NumberToObjectMap} from "@eccosolutions/ecco-common";
import {ApiClient} from "../web-api";
import {Building, OccupancyHistoryDto} from "../building-dto";
import {BuildingRepository, OccupancyFilter} from "./BuildingRepository";

export class BuildingAjaxRepository implements BuildingRepository {
    private allBuildings: Promise<NumberToObjectMap<Building>> | null = null;

    constructor(private apiClient: ApiClient) {}

    public findOneBuilding(buildingId: number): Promise<Building> {
        return this.apiClient.get<Building>(`buildings/${buildingId}/`);
    }

    public findOneBuildingBySrId(srId: number): Promise<Building> {
        return this.apiClient.get<Building>(`buildings/byServiceRecipient/${srId}/`);
    }

    public findAllBuildingsInIds(buildingIds: number[]): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/byIds/", {
            query: {
                ids: buildingIds.join(",")
            }
        });
    }

    public findAllBuildingsOf(buildingId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/${buildingId}/children/`);
    }

    public findAllBuildingCareRuns(buildingId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/${buildingId}/careruns/`);
    }

    public findAllBuildings(query?: {
        resourceType?: string;
        showChildren?: "true" | "false";
        addressLocationId?: string;
    }): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/", {query});
    }

    // NB hierarchical from a single location
    // UNUSED
    public findAllBuildingsOfLocationId(addressLocationId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/byLocation/${addressLocationId}/`);
    }

    // NB non-hierarchical
    public findAllBuildingsOfLocationIds(addressLocationIds: number[]): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/byLocationIds/", {
            query: {
                ids: addressLocationIds.join(",")
            }
        });
    }

    public findAllBuildingsForUser(query?: {
        resourceType?: string;
        showChildren?: "true" | "false";
    }): Promise<Building[]> {
        // FIXME: Implement back end filtering when we know what the logic should be
        return this.findAllBuildings(query);
    }

    public getCachedBuildingsMap(refresh = false) {
        if (!this.allBuildings || refresh) {
            this.allBuildings = this.findAllBuildings().then(buildings => {
                const index = {} as NumberToObjectMap<Building>;
                buildings.forEach(building => {
                    index[building.buildingId] = building;
                });
                return index;
            });
        }
        return this.allBuildings;
    }

    /**
     * Usage is designed for either:
     *  - all history paged, or
     *  - all history for a set of buildingId(s) within the date range
     */
    public findOccupancyHistory(
        from: EccoDate,
        to: EccoDate,
        filter: OccupancyFilter,
        search: string | null,
        page?: number,
        buildingIds?: number[]
    ): Promise<OccupancyHistoryDto[]> {
        const query: Record<string, string> = {
            from: from.formatIso8601(),
            to: to.formatIso8601()
        };
        if (page) {
            query.page = page.toString();
        }
        if (buildingIds) {
            query.buildingIds = buildingIds.join(",");
        }
        if (filter) {
            query.filter = filter;
        }
        if (search) {
            query.search = search;
        }
        return this.apiClient.get<OccupancyHistoryDto[]>("occupancyhistory/", {query});
    }
}
