/**
 * A Mergeable is a command that can be merged with other commands of the same type to result in a modified combination,
 * or an empty object.
 *
 * Add/remove scenarios are easy, but merging text changes would be more difficult.  If we want them to become
 * possible to merge.  This means we need to know the previous state.
 *
 * NOTE: It is useful to have all commands also be reverseable commands (for undo).  If the command knows its
 * previous state then an undo is possible.
 *
 * Take care with commands that are arithmetic in nature (add 3) vs setting a required value (change x from 4 to 7).
 * There may be valid scenarios for both types of change.
 */
export interface Mergeable {
    /** Return true if this command can merge with a previous one */
    canMerge(candidateToMerge: Mergeable): boolean;

    /**
     * Return the result of merging this command into previous.  Note: Order is important.
     *
     * The result will be null if the commands cancel out.
     */
    merge(previousCommand: this): this | null;
}

export type DtoFieldValue = string | number | boolean | Change<any> | {[key:string]: Change<any>} | object | null | undefined;

/** This is what allows the destination of the command to be identified.  It is possible that this may be aggregated
 *  in a larger piece of work ... or preferably not.
 *  The plan is to allow the changes to be automatically merged when viewing history.  we could allow a command
 *  to have a parent guid ref or something where they are submitted as a "save" action.  We'd like to get away from that
 *  however, and just fire the commands off to the server as we go.
 *  More likely they will be held in a queue until "save" is hit.
 *
 *  Must match the Java class com.ecco.webApi.evidence.BaseCommandViewModel. */
export interface CommandDto {
    /**
     * A UUID uniquely identifying the command.
     *
     * Used for de-duplication.
     */
    uuid: string;

    /**
     * The URI identifying the type of command, relative to the Web API
     * root path.
     *
     * This is the URI to which the command object was POSTed, or to which it
     * shall be POSTed.
     */
    commandUri: string;

    /**
     * The date and time at which the command was created.
     *
     * This value is formatted in ISO 8601 extended format
     * ("YYYY-MM-DDTHH:MM:SSZ"). The time zone is always UTC.
     */
    timestamp: string;

    draft?: boolean;

    /**
     * The userName of the account that submitted the command to the server.  This is optional as it is only
     * populated when retrieving the commands back from the server.
     */
    userName?: string;

    /**
     * The userName of the account that submitted the command to the server.  This is optional as it is only
     * populated when required when retrieving the commands back from the server.
     */
    userDisplayName?: string;

    /**
     * The commandName is the discriminator between different commands as stored in that column in the remote database
     * command table.
     *
     * This is mandatory when using commands offline, as a domain object with related effects is required.
     *
     * Preferred value is that the value of this field is equal to the name of the DTO interface and matching
     * domain object that defines this command, for example "SignWorkCommand".
     */

    commandName?: string;
}

/** For compatibility with older code */
export interface UpdateCommandDto extends CommandDto {
    operation?: string; // TODO restrict to what is expected at server end: "add" | "update";
}


export type Change<T> = {
    from: T | null;
    to: T | null;
}

export type ChangeOptional<T> = Change<T> | undefined;

export type AddedRemoved<T> = {
    from?: T[]
    added?: T[]
    removed?: T[]
};

export type AddedRemovedOptional<T> = AddedRemoved<T> | undefined

export type BooleanChange = ChangeOptional<boolean>;

export type StringChange = Change<string>;
export type StringChangeOptional = ChangeOptional<string>;

export type NumberChange = Change<number>;
export type NumberChangeOptional = ChangeOptional<number>;

export type ArrayChange<T> = ChangeOptional<T[]>;

export type NumberAddedRemoved = AddedRemovedOptional<number>;

export type StringAddedRemoved = AddedRemovedOptional<string>;

/** Associated 'thing' changes: here added/removed is the correct approach and allows merging */
export interface ReferenceChanges {
    addedIds: number[];
    removedIds: number[];
}

