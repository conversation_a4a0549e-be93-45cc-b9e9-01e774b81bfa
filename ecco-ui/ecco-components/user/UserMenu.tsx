import {applicationRootPath} from "application-properties";
import * as React from "react";
import {FC, ReactElement, useState} from "react";
import {Divider, Hidden, MenuItem, withStyles} from "@eccosolutions/ecco-mui";
import {IconMenu} from "@eccosolutions/ecco-mui-controls";
import {useServicesContext} from "../ServicesContext";
import {RECENT_USER_CHANGE} from "ecco-dto";
import {ChangePasswordForm} from "./ChangePasswordForm";
import {showNotification} from "../MUIComponentUtils";
import {WebApiError} from "@eccosolutions/ecco-common";
import {EccoTheme} from "../theme";

export const UserMenu: FC<{
    extraMenuItems?: null | ReactElement | ReactElement[];
    hideUsername?: boolean;
}> = ({extraMenuItems, hideUsername}) => {
    const [open, setOpen] = useState(false);
    const [changePwd, setChangePwd] = useState(false);
    const {apiClient, sessionData, userRepository} = useServicesContext();

    if (!sessionData.getDto().individualUserSummary) {
        console.error("Missing individualUserSummary in sessionData: %o", sessionData.getDto()); // FIXME: split user and global data so auth path is clear
        return <span />; // Just don't show user menu if stuff is screwed up so we get chance to do login
    }

    const MenuItemCenter = withStyles({
        root: {
            justifyContent: "center"
        }
    })(MenuItem);

    return (
        <span>
            {!hideUsername && (
                <Hidden xsDown>{sessionData.getDto().individualUserSummary.username}</Hidden>
            )}
            <IconMenu
                id="login-menu"
                iconClasses={`fa fa-user${
                    sessionData.hasRolePreviousAdministrator() ? " fa-spin" : ""
                }`}
                color={sessionData.hasRolePreviousAdministrator() ? "secondary" : undefined}
                open={open}
                onClose={() => setOpen(false)}
                onClick={() => setOpen(true)}
            >
                <MenuItemCenter disabled={true}>
                    {sessionData.getDto().individualUserSummary.username}
                </MenuItemCenter>
                <Divider />
                <MenuItem onClick={() => setChangePwd(true)}>
                    <i className="fa fa-key" />
                    &nbsp;change password
                </MenuItem>

                {/* TODO: Enable menu items to be contributed via an event bus that sets menuitems (name, onClick) here onMount
                      and removes them on unmount.  Could have some 'selected' flag too
                <c:if test="${ROLE_ADMIN && !empty adminMenuText || canDoSysAdmin}">
                    <requireJs:import modules="admin/enable-editor"/>
                    <li><a class="menu-action-edit-mode" role="button" href="#">
                        <i class="fa fa-cog"></i> ${empty adminMenuText ? "config mode" : adminMenuText}</a></li>
                </c:if>
                */}
                {extraMenuItems}
                {sessionData.hasRoleSysAdmin() && (
                    <MenuItem
                        onClick={() => {
                            sessionStorage.setItem(RECENT_USER_CHANGE, "ecco_staff");
                            return apiClient
                                .post(
                                    `${applicationRootPath}nav/secure/admin/switchUser?username=ecco_staff`,
                                    null
                                )
                                .then(() => window.location.reload());
                        }}
                    >
                        <i className="fa fa-user-secret" />
                        &nbsp;impersonate 'staff' user
                    </MenuItem>
                )}
                {sessionData.hasRolePreviousAdministrator() && (
                    <MenuItem
                        onClick={() => {
                            sessionStorage.setItem(RECENT_USER_CHANGE, "[prev_administrator]");
                            return apiClient
                                .post(`${applicationRootPath}nav/secure/admin/switchUserExit`, null)
                                .then(() => window.location.reload());
                        }}
                    >
                        <i className="fa fa-user-secret" />
                        &nbsp;stop impersonating
                    </MenuItem>
                )}

                {/* NB window.location.reload works for app and website access */}
                {/* however we need a redirect since shared-devices retain the last url of potentially inaccessible referrals (and logoutSucess is not set on spring-boot */}
                <MenuItem
                    onClick={() =>
                        apiClient
                            .post(`${applicationRootPath}nav/secure/logout.html`, null)
                            /* NB we observe a cancelled GET request to root path when 'then' is triggered */
                            .then(() =>
                                window.location.replace(
                                    /* TODO reinstate the logout man on the login page? */
                                    `${applicationRootPath}nav/secure/welcome.html`
                                )
                            )
                    }
                >
                    <i className="fa fa-sign-out" />
                    &nbsp;sign out
                </MenuItem>
            </IconMenu>

            {changePwd && (
                /* could have used a context, as per 50d73167 */
                <EccoTheme prefix="userMenu-breakout">
                    <ChangePasswordForm
                        onChange={pwd => {
                            if (!pwd) {
                                setChangePwd(false);
                                return;
                            }
                            userRepository
                                .updateMyPassword(
                                    sessionData.getDto().individualUserSummary.username,
                                    pwd
                                )
                                .then(() => {
                                    showNotification("info", "password updated");
                                    setChangePwd(false);
                                })
                                .catch((errorResult: WebApiError) => {
                                    // msg is the lookup message OR the message itself if there is no code
                                    var msg =
                                        // @ts-ignore
                                        sessionData.getMessages()[errorResult.reason.message] ||
                                        errorResult.reason.message;
                                    showNotification("error", msg);
                                });
                        }}
                    />
                </EccoTheme>
            )}
        </span>
    );
};
UserMenu.displayName = "UserMenu"