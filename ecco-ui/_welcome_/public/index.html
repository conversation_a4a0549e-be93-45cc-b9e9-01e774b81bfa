<!DOCTYPE html>
<!--suppress HtmlUnknownTarget, JSUnresolvedLibraryURL -->
<html lang="en" class="v3">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Auditable management for care and support"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/ecco-e-192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json">
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico">
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
          integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link href="%PUBLIC_URL%/common.css" rel="stylesheet">
    <link href="%PUBLIC_URL%/evidence.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script> <!-- TODO: Remove both of these when we've migrated tabs and pickers -->
    <script src="https://code.jquery.com/ui/1.10.3/jquery-ui.min.js"></script> <!-- TODO: Remove both of these when we've migrated tabs and pickers -->
    <script src="https://cdn.jsdelivr.net/npm/lazy.js@0.5.1/lazy.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/urijs@1.19.2/src/URI.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>ecco - Care and Support</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      function scoped() { // Use a function to prevent vars leaking to global scope
        const username = "%REACT_APP_USERNAME%";
        window.global_credentials = username && { // Specify these in .env.local
          username,
          password: "%REACT_APP_PASSWORD%"
        };
        const publicUrl = "%PUBLIC_URL%";
        if (publicUrl === "" || publicUrl === "http://localhost:3000") { // Empty when running "yarn start"
          // NOTE: We keep these to allow us to override when operating locally

          // NB external sites from localhost REQUIRE samesite=none, ie those with 365 configured

          // window.remoteRoot = "https://test.eccosolutions.co.uk/testdomcare";
          //window.remoteRoot = "https://test.eccosolutions.co.uk/testhousing";
          window.remoteRoot = "https://demo.eccosolutions.co.uk/housing";
          //window.remoteRoot = "https://demo.eccosolutions.co.uk/uat";
          //window.remoteRoot = "http://localhost:8888/ecco-war";
          // window.remoteRoot = "http://localhost:8899";
        } else {
          // Remove the tail from e.g. http://localhost:8888/ecco-war/r/app/rota?blah
          window.remoteRoot = publicUrl.split("/r/app")[0];
        }

        console.assert(!remoteRoot.endsWith("/"));

        window.appPath = publicUrl.length > 0 ? new URL(publicUrl).pathname + "/" : "/";
        const applicationRootPath = remoteRoot + "/";

        window.applicationProperties = {
          applicationRootPath: applicationRootPath, // NOTE: remoteRoot is more useful in SPA setting. We may need to make this absolute for SPA i.e. https://host/contextPath so we can link back to full app where needed
          resourceRootPath: "/ui/"
        };

        window.notAvailOffline = {
          default: () => {
            console.error("not available offline")
          }
        }
      }
      scoped();
    </script>
    <div id="appbar"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
