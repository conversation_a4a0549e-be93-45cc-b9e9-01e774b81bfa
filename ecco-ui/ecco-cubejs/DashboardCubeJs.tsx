import * as React from "react";
import App from "./App";
import {Box} from '@eccosolutions/ecco-mui';

/**
 * NB serviceRecipientId comes from SvcRecPageRouter usePageComponentLookup
 * which passes it to the component - so we could add contactId, but instead simply load it server-side for simplicity
 *
 */
export function DashboardCubeJs(props: {}) {
    return <Box m={1}>
        CUBE js2
        <App/>
    </Box>;
}
