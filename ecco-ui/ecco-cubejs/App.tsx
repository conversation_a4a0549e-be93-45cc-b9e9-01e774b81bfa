import * as React from "react";
import cube, {PivotConfig, Query} from "@cubejs-client/core";
import {CubeProvider} from "@cubejs-client/react";
import WebSocketTransport from "@cubejs-client/ws-transport";
import {ChartViewer} from "./ChartViewer";
import {extractHashConfig} from "./config";
import {QueryRenderer} from "./QueryRenderer";
import {ChartType, Config} from "./types";

const env = {
    VITE_CUBE_API_URL: "http://localhost:4004/cubejs-api/v1",
    VITE_CUBE_API_TOKEN:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************.Gv_d2cEW_CbV4HhzeietkZIrcyCYhZ71yLcr83NqJj8",
    VITE_CUBE_QUERY:
        '{"dimensions":["referrals_clients.ethnicity"],"measures":["referrals_clients.count"]}',
    VITE_CUBE_PIVOT_CONFIG:
        '{"x":["referrals_clients.ethnicity"],"y":["measures"],"fillMissingDates":true,"joinDateRange":false}',
    VITE_CHART_TYPE: "line",
    VITE_CUBE_API_USE_WEBSOCKETS: false,
    VITE_CUBE_API_USE_SUBSCRIPTION: false
};
function App() {
    const {apiUrl, apiToken, query, pivotConfig, chartType, useWebSockets, useSubscription} =
        extractHashConfig({
            apiUrl: env.VITE_CUBE_API_URL || "",
            apiToken: env.VITE_CUBE_API_TOKEN || "",
            query: JSON.parse(env.VITE_CUBE_QUERY || "{}") as Query,
            pivotConfig: JSON.parse(env.VITE_CUBE_PIVOT_CONFIG || "{}") as PivotConfig,
            chartType: env.VITE_CHART_TYPE as ChartType,
            websockets: env.VITE_CUBE_API_USE_WEBSOCKETS,
            subscription: env.VITE_CUBE_API_USE_SUBSCRIPTION
        } as Config);

    let transport = undefined;

    if (useWebSockets) {
        transport = new WebSocketTransport({authorization: apiToken, apiUrl});
    }

    const cubeApi = cube(apiToken, {apiUrl, transport});

    return (
        <>
            <CubeProvider cubeApi={cubeApi}>
                <QueryRenderer query={query} subscribe={useSubscription}>
                    {({resultSet}) => {
                        return (
                            <ChartViewer
                                chartType={chartType}
                                resultSet={resultSet}
                                pivotConfig={pivotConfig}
                            />
                        );
                    }}
                </QueryRenderer>
            </CubeProvider>
        </>
    );
}

export default App;
