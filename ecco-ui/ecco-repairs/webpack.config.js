// const config = require("@softwareventures/webpack-config");
//
// module.exports = config({
//     title: "Ecco Commands",
//     vendor: "ecco",
//     entry: "./index.ts"
// });

var path = require("path");
var TerserJsPlugin = require("terser-webpack-plugin");

var projectDir = __dirname;
var distDir = path.resolve(projectDir, "./dist/");
var debugDir = path.resolve(projectDir, "./debug/");

function configuration(mode) {
    const notRelativePath = /^(?!\.\.?\/)/;
    var configuration = {
        mode: mode,
        name: mode === "production" ? "prod" : "dev",
        entry: "./index.ts",
        externals: [
            notRelativePath, // DOES THIS DO EVERYTHING?
            ///^@ecco[a-z-]+/,
            ///^ecco-[a-z-]+$/, // don't want relative stuff like ./ecco-events externalised
            /ecco-[a-z-]+$/, // works for @eccosolutions/ecco- too, but don't want .css .png etc to be externalised
            "@material-ui/core",
            "@material-ui/styles",
            "application-properties",
            "bowser",
            "lodash", // We actually shouldn't have this, but webpack will put all of it in if we allow it to
            "moment",
            "react",
            "react-async",
            "react-bootstrap",
            "react-dom",
            "react-router",
            "react-router-dom"
        ],
        module: {
            rules: [
                {
                    test: /\.js$/,
                    loader: "babel-loader",
                    options: {
                        presets: ["@babel/preset-env"]
                    }
                },
                {
                    test: /\.tsx?$/,
                    loader: "esbuild-loader",
                    options: {
                        loader: "tsx", // Or 'ts' if you don't need tsx
                        target: "es2015"
                    },
                    exclude: /(^|\/)node_modules\//
                }
            ]
        },
        resolve: {
            extensions: [".tsx", ".ts", ".js"]
        },
        output: {
            path: mode === "production" ? distDir : debugDir,
            filename: "ecco-repairs.js",
            libraryTarget: "umd",
            devtoolModuleFilenameTemplate: "[resource-path]?[loaders]"
        }
    };

    if (mode === "production") {
        configuration.optimization = {
            minimizer: [
                new TerserJsPlugin({
                    parallel: true,
                    terserOptions: {
                        compress: {
                            passes: 2
                        }
                    }
                })
            ]
        };
    } else {
        configuration.devtool = "inline-source-map";
    }

    return configuration;
}

module.exports = [configuration("production"), configuration("development")];
