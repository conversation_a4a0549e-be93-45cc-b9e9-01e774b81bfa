package com.ecco.buildings.dom;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.UUID;

/**
 * Commands around addresses.
 */
@Entity
@Table(name = "bldg_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("adr")
public class AddressCommand extends BaseIntKeyedCommand {

    @Column(nullable=false)
    private Integer addressLocationId;

    public AddressCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                          long userId, @Nonnull String body, Integer addressLocationId) {
        super(uuid, remoteCreationTime, userId, body);
        this.addressLocationId = addressLocationId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected AddressCommand() {
        super();
    }

    public int getAddressLocationId() {
        return addressLocationId;
    }
}
