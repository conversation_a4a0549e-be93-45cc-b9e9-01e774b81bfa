package com.ecco.buildings.dom;

import java.util.Collections;
import java.util.Map;

import javax.persistence.*;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.Individual;
import com.ecco.dom.contacts.AddressLike;

@Entity
@DiscriminatorValue(BuildingServiceRecipient.DISCRIMINATOR)
public class BuildingServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "bldg";
    public static final String PREFIX = "b"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade= CascadeType.REMOVE)
    private FixedContainer building;

    @Override
    public String getDisplayName() {
        return building.getName();
    }

    @Override
    public AddressLike getAddress() {
        return building.getLocation();
    }

    @Override
    public String getCalendarId() {
        return building.getCalendarId();
    }

    @Override
    public Individual getContact() {
        return null; // a building doesn't have one
    }

    @Override
    public Map<String,String> getTextMap() {
        return Collections.emptyMap();
    }

    @Override
    public FixedContainer getTargetEntity() {
        return building;
    }

    public FixedContainer getBuilding() {
        return building;
    }

    void setBuilding(FixedContainer referral) {
        this.building = referral;
    }

    @Override
    public String getParentCode() {
        return building.getId().toString();
    }

    @Override
    public Long getParentId() {
        return building.getId().longValue();
    }

    @Override
    public String getPrefix() {
        return "b";
    }
}
