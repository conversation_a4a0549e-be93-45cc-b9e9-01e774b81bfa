package com.ecco.service;

import com.ecco.calendar.dom.EventType;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dao.*;
import com.ecco.dom.*;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.ReferralLegacyToViewModel;
import com.ecco.dto.ReferralRelationshipDto;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.hibernate.AntiProxyUtils;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.hibernate.EntityUriMapper.EntityComponents;
import com.ecco.infrastructure.hibernate.HibFilterTemplate;
import com.ecco.security.dom.User;
import com.ecco.security.service.UserManagementService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.workflow.WorkflowService;
import com.ecco.calendar.core.Entry;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

import static com.ecco.dao.ReferralPredicates.clientNameIsLike;
import static com.ecco.dao.ReferralRepository.SORT_BY_CLIENT_ID;

@org.springframework.stereotype.Service("referralService")
@WriteableTransaction
public class ReferralServiceImpl implements ReferralService {

    @Autowired
    private ServiceTypeService serviceTypeService;

    @Autowired
    private EntityRestrictionService entityRestrictionService;

    private WorkflowService workflowService;
    private ReferralDao referralDao;

    @Autowired
    ClientRepository clientRepository;

    @Autowired
    ReferralRepository referralRepository;

    @Autowired
    EventService eventService;

    @Autowired
    CustomEventRepository nonRecurringRepository;

    @Autowired
    ServiceCategorisationRepository serviceCategorisationRepository;

    @Autowired
    private EntityUriMapper entityUriMapper;


    @Resource(name = "userManagementService")
    private UserManagementService userManagementService;

    @Autowired
    private ServiceRecipientRepository serviceRecipientRepository;

    @Autowired
    private SoftwareModuleService softwareModuleService;

    @Autowired
    private MessageSourceAccessor messageSourceAccessor;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    public void setReferralDao(ReferralDao referralDao) {
        this.referralDao = referralDao;
    }

    @Autowired
    @Qualifier("activitiWorkflowService")
    public void setWorkflowService(WorkflowService workflowService) {
        this.workflowService = workflowService;
    }

    @Override
    public Iterable<Referral> queryReferralsByClientExample(final ClientDefinition exemplar, boolean includeHidden) {
        final Supplier<Iterable<Referral>> action = () -> referralRepository.findAll(
                clientNameIsLike(exemplar.getLastName(), exemplar.getFirstName()), SORT_BY_CLIENT_ID);

        if (includeHidden) {
            return createHibFilterTemplate().executeUnfiltered(action);
        } else {
            return action.get();
        }
    }

    /** Get the id of the referral associated with this entry if there is one associated, otherwise return null */
    @Override
    public Long getReferralId(Entry entry) {
        EntityComponents components = entityUriMapper.componentsForEntity(entry.getManagedByUri());

        if (components != null && CustomEventWithServiceRecipient.class.equals(components.getEntityClass())) {
            final int srId = serviceRecipientRepository.findServiceRecipientIdForCustomEvent(components.getId());
            return referralRepository.getReferralIdByServiceRecipientId(srId);
        }

        if (components != null && CustomEventRecurringImpl.class.equals(components.getEntityClass())) {
            var srId = serviceRecipientRepository.findServiceRecipientIdForCustomRecurringEvent(components.getId());
            return srId.map(integer -> referralRepository.getReferralIdByServiceRecipientId(integer)).orElse(null);
        }

        // Rota events should also link back to the referral.
        if (components != null && DemandSchedule.class.isAssignableFrom(components.getEntityClass())) {
            Integer srId = serviceRecipientRepository.findServiceRecipientIdForDemandSchedule(components.getId());
            BaseServiceRecipient sr = serviceRecipientRepository.findById(srId).orElse(null);
            if (sr instanceof ReferralServiceRecipient) {
                return ((ReferralServiceRecipient) sr).getReferral().getId();
            }
        }
        return null;
    }

    @Override
    public Referral getReferral(Long id) {
        return initializedReferral(referralRepository.getById(id));
    }

    private Referral initializedReferral(Referral toReturn) {
        var adr = toReturn.getClient().getContact().getAddressedLocation();
        if (adr != null) {
            //noinspection ResultOfMethodCallIgnored
            adr.getLine1(); // force load
        }
        return toReturn;
    }

    @Override
    public ModelMap getLegacyReferralDetails(long referralId) {
        var vm = new ReferralLegacyToViewModel(this, serviceTypeService, workflowService, softwareModuleService);
        return vm.apply(referralId);
    }

    /**
     * Find the primaryReferralId for this referral, which could be the referral passed depending on whether the system
     * is configured for allowing multiple referrals (family support).
     * @param referral to find the primary referral id
     * @return the primary referral id, or null if no such relationships exist
     */
    @Override
    public Long getPrimaryReferralId(Referral referral) {
        // if a primary referral exists, which indicates is has to be a 'child'
        if (referral.getPrimaryReferral() != null) {
            return AntiProxyUtils.identifier(referral.getPrimaryReferral());
        }
        // if we have a 'multiple referral' service type aspect (which is not set to display) then we must be the primary
        if (hasGlobalTaskDefinition(referral.loadConfigServiceTypeId(), TaskDefinition.NEWMULTIPLE_REFERRAL)) {
            return referral.getId();
        }
        return null;
    }

    // LINEAR workflow only - so if activiti, also include the appropriate linear settings
    private boolean hasGlobalTaskDefinition(long serviceTypeId, long taskDefinitionId) {
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(Long.valueOf(serviceTypeId).intValue());
        Optional<TaskDefinitionEntryViewModel> td = serviceType.taskDefinitionEntries.stream().filter(entry -> entry.taskDefId == taskDefinitionId).findFirst();
        return td.isPresent();
    }

    // called by referralFlow.xml if the serviceType has the multiple referrals referral aspect
    @Override
    public List<ReferralRelationshipDto> getRelationshipReferralsDto(long primaryReferralId) {
        List<Referral> entities = this.referralRepository.findAllByPrimaryReferral_Id(primaryReferralId);
        entities.add(this.referralRepository.findById(primaryReferralId).orElse(null));
        List<ReferralRelationshipDto> referrals = new ArrayList<>();
        if (!entities.isEmpty()) {
            for (Referral referral : entities) {
                ReferralRelationshipDto dto = new ReferralRelationshipDto();
                dto.setPrimaryReferralId(primaryReferralId);
                dto.setClientDisplayName(referral.getClient().getDisplayName());
                if (referral.getClient().getBirthDate() != null) {
                    dto.setBirthDate(referral.getClient().getBirthDate().toLocalDate());
                }
                dto.setClientId(referral.getClient().getId());
                dto.setReferralId(referral.getId());
                if (referral.getRelationshipToPrimaryReferral() != null) {
                    dto.setRelationship(referral.getRelationshipToPrimaryReferral().getName());
                }
                referrals.add(dto);
            }
        }
        return referrals;
    }

    // just for fun really, to play with 'merge'
    // really the referral save/load in webflow should call referralService.getReferral not getEntity
    // because our lazy mappings on the contacts are essential to keep the loads lean
    // although we could always join them, but this is about the only place (referral overview 'contacts') that they are used


    @Override
    public void setReferralWithInterviewDate(Referral referral) {

        // set the first response if not done already
        if (referral.getFirstResponseMadeOn() == null) {
            // and 'interview date' isn't blank - as could just save without the details in
            if (referral.getInterviewDate() != null) {
                // firstResponseMadeOn is now a user's local date mainly for use in 'next meeting date'
                // so we save 'now' with the users' timezone
                referral.setFirstResponseMadeOn(DateTimeUtils.convertFromUsersLocalDateTime(DateTimeUtils.getLocalNow()));
            }
        }

        // quick solution to saving the 'currentTaskIndex' before its reloaded below and lost
        // (we don't want to start investigating removing the getEntity, or changing Cascade at the moment)
        entityManager.merge(referral.getServiceRecipient());

        // NOTE: Swapped with above as we don't want to do preUpdate on stale data, so we want merge of data updated via web API first
        // save the referral (there is no event information in this)
        referral = referralRepository.save(referral); // causes @PreUpdate to trigger which makes changes to servicerecipient

        // hibernate isn't liking using referral to do lazy loading, so we load the referral again, and it works
        //referral = entityService.getEntity(Referral.class, referral.getId());

        ReferralServiceImpl.deleteDates(nonRecurringRepository, eventService, referral.getServiceRecipientId(), EventType.Interview, null);

        // create the new event - check the criteria for a new event does exist (eg we need an individual)
        if ((referral.getInterviewer1() == null) && (referral.getInterviewer2() == null)) {
            // we 'remove' existing appointments through the delete above
            return;
        }

        Assert.notNull(referral.getInterviewDate(), "Decision date must be provided");

        if (referral.getInterviewer1() != null) {
            Individual interviewer1 = referral.getInterviewer1();
            Individual interviewer2 = referral.getInterviewer2();
            if (interviewer2 != null && !interviewer2.getId().equals(interviewer1.getId())) {
                ReferralServiceImpl.setDatesForReferralWithContacts(eventService, referral,
                        referral.getInterviewDate(), referral.getInterviewLocation(), EventType.Interview, interviewer1, interviewer2);
            } else {
                ReferralServiceImpl.setDatesForReferralWithContacts(eventService, referral,
                        referral.getInterviewDate(), referral.getInterviewLocation(), EventType.Interview, interviewer1);
            }
        }
    }

    /**
     * Sets dates in the diary of the client with attendees of the individuals.
     * Typically used in setting interview dates, and review dates (see ReviewServiceImpl.scheduleReviewDates)
     */

    public static void setDatesForReferralWithContacts(EventService eventService, Referral referral, DateTime datetime, String location, EventType type, Individual... individuals) {
        Individual clientContact = referral.getClient().getContact();
        assert clientContact != null;
        CustomEventWithServiceRecipient event = EventUtils.createEventForIndividual(referral.getServiceRecipientId(), clientContact, datetime, type, location);

        // We'll receive an array with one null entry if e.g. there is no support worker assigned
        for (Individual individual : individuals) {
            if (individual != null) {
                // each contact is added as an attendee for the calendar support handler use of getCalendarIdsForNewEvent
                event.addNewCalendarId(individual.getCalendarId());

                // associate an entry with the client, so that we can assign a status - eg did not attend
                final Contact_Event ec = new Contact_Event();
                ec.setEvent(event);
                ec.setContact(individual);
                event.getContactsEvents().add(ec);
            }
        }

        eventService.persistAndSyncAddNonRecurringCalendarEntry(event);
    }

    public static void deleteDates(CustomEventRepository nonRecurringRepository, EventService eventService, int srId,
                                   EventType type, @Nullable LocalDate from) {
        // we delete the future scheduled dates, since past ones could one day have additional information associated with them
        // based on EvidenceServiceImpl.setDatesInCalendar
        // at the moment, we are handling the crud operations on events 'manually' for 2 reasons
        // 1) if we used the hibernate 'touch' approach on the set it would load too much? on a OneToMany (referral -> events)
        // 2) if we used the hibernate way, it would need to be tightly coupled to cosmo
        var fromOrEpoch = from != null ? from : Instant.ofEpochMilli(0).toDateTime().toLocalDate();
        var entities = nonRecurringRepository.findAllByTypeGreaterThan(srId, type, fromOrEpoch.year().get(), (short) fromOrEpoch.monthOfYear().get(), (short) fromOrEpoch.dayOfMonth().get());
        if ((entities != null) && (!entities.isEmpty())) {
            for (var entity : entities) {
                eventService.persistAndSyncRemoveNonRecurringCalendarEntry(entity);
            }
        }
    }

    @Override
    public Long setNewClient(ClientDetail client) {
        User user = userManagementService.generateClientUser(client.getDisplayName(), client.getContact());
        client.getContact().setUser(user);
        client = clientRepository.save(client);

        createClientCodeIfNotSet(client);
        return client.getId();
    }

    private void createClientCodeIfNotSet(ClientDetail client) {
        if (client.getCode() != null) {
            return;
        }
        // now we have the id, save again setting the code
        String lastClientID = messageSourceAccessor.getMessage("lastClientID");
        if (StringUtils.isEmpty(lastClientID)) {
            lastClientID = "0";
        }
        // always save a code, since we want to use this in displays and lookups...
        long lastClientIDnum = Long.parseLong(lastClientID);
        long thisClientIDnum = lastClientIDnum + client.getId();
        client.setCode(String.valueOf(thisClientIDnum));
        clientRepository.save(client);
    }

    @Override
    public void setReferral(Referral referral) {
        boolean isNew = referral.isNewEntity();

        if (!isNew) {
            entityManager.merge(referral.getServiceRecipient());
            referralRepository.save(referral);
        }
        else {
            referralRepository.save(referral);
            serviceRecipientRepository.save(referral.getServiceRecipient()); // because some things have moved to project
        }

        if (isNew) {
            createReferralCodeIfNotSet(referral);
            instiatiateWorkflowIfRequired(referral);
        }
    }

    private void instiatiateWorkflowIfRequired(Referral referral) {
        // If this is a workflow-backed referral, start the workflow...
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(referral.loadConfigServiceTypeId());
        Optional<ServiceTypeWorkflow> workflow = serviceTypeService.findWorkflow(serviceType.id);
        if (workflow.isPresent()) {
            workflowService.instantiateWorkflow(serviceType.processKey, referral.getServiceRecipientId().toString());
        }
    }

    private void createReferralCodeIfNotSet(Referral referral) {
        if (referral.getCode() != null) {
            return;
        }
        // now we have the id, save again setting the code
        String lastReferralID = messageSourceAccessor.getMessage("lastReferralID");
        if (StringUtils.isEmpty(lastReferralID)) {
            lastReferralID = "0";
        }
        // always save a code, since we want to use this in displays and lookups...
        long lastReferralIDnum = Long.parseLong(lastReferralID);
        long thisReferralIDnum = lastReferralIDnum + referral.getId();
        referral.setCode(String.valueOf(thisReferralIDnum));
        referralRepository.save(referral);
    }

    @Override
    public void setCustomProperties(Referral referral) {
        referralDao.setCustomProperties(referral);
    }

    @Override
    public List<Referral> getCustomProperties(List<Long> ids, Long serviceId) {
        return referralRepository.findAllCustomFields(ids);
    }

    @Override
    public void setPropertyTimestamp(long referralId, String property, DateTime datetime) {
        referralDao.setPropertyTimestamp(referralId, property, datetime);
    }

    @Override
    public void moveReferralToService(long referralId, long serviceId) {
        Referral fromReferral = referralRepository.findById(referralId).orElseThrow();
        var svcCatFrom = serviceCategorisationRepository.getById(fromReferral.getServiceRecipient().getServiceAllocationId());
        var svcCatTo = serviceCategorisationRepository.findOneByService_IdAndProject_Id(serviceId, svcCatFrom.getProject() != null ? svcCatFrom.getProject().getId() : null);
        fromReferral.setServiceAllocation(svcCatTo);
        referralRepository.save(fromReferral);
    }

    @Override
    public void moveReferralToClient(long referralId, long clientId) {
        Referral fromReferral = referralRepository.findById(referralId).orElseThrow();
        ClientDetail toClient = clientRepository.findById(clientId).orElseThrow();
        fromReferral.setClient(toClient);
        referralRepository.save(fromReferral);
    }

    @Override
    public void hideReferral(long referralId) {
        Referral existing = referralRepository.findById(referralId)
            .orElseThrow(() -> new IllegalArgumentException("referralId must match an existing non-hidden referral"));

        existing.requestDelete();
        referralRepository.save(existing);
    }

    @Override
    public void hideReferralByServiceRecipientId(int serviceRecipientId) {
        @SuppressWarnings("ConstantConditions")
        long referralId = referralRepository.getReferralIdByServiceRecipientId(serviceRecipientId);
        hideReferral(referralId);
    }

    @Override
    public void unhideReferral(final long referralId) {
        Referral existing = createHibFilterTemplate().executeUnfiltered(
                () -> referralRepository.findOneByIdAndRequestedDeleteIsNotNull(referralId));
        Assert.notNull(existing, "referralId must match an existing hidden referral");
        existing.cancelDeleteRequest();
        referralRepository.save(existing);
    }

    @Override
    public void unhideReferralByServiceRecipientId(int serviceRecipientId) {
        @SuppressWarnings("ConstantConditions")
        long referralId = referralRepository.getReferralIdByServiceRecipientId(serviceRecipientId);
        unhideReferral(referralId);
    }

    HibFilterTemplate createHibFilterTemplate() { // Allow tests to override with a mock.
        return new HibFilterTemplate(entityManager);
    }
}
