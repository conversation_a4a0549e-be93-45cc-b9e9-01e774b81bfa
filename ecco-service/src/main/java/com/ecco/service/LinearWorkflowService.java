package com.ecco.service;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.TaskDefinitionNameIdMappings.Task;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.workflow.*;
import com.ecco.workflow.WorkflowTask.Handle;
import com.ecco.workflow.WorkflowTaskDefinition.TaskDefinitionHandle;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.net.URI;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.ecco.serviceConfig.dom.ServiceTypeWorkflow.LINEAR_WORKFLOW_PROCESS_KEY;
import static java.util.Comparator.*;
import static java.util.stream.Collectors.groupingBy;

/**
 * A task engine to be used as a simplified approach to that taken by BPMN systems such as Activiti.
 *
 * The task lifecycle should match Activiti as we'll be querying both.
 * Lifecycle:
 * - no task exists
 * - task exists but is not assigned to anyone
 * - task potentially becomes overdue before being assigned
 * - someone gets assigned
 * - task potentially becomes overdue after being assigned
 * - task is closed
 *
 * This matches workflow engines, of which Activiti is one.
 *
 * We should not assign all created tasks.
 * We should differentiate between
 * - ecco_neale started a task and it is overdue
 * and
 * - ecco_neale is the key worker, and there is an unclaimed overdue task
 */
@Service("linearWorkflow")
@RequiredArgsConstructor
public class LinearWorkflowService implements WorkflowService {

    private static final String CLOSE_OFF = "close";
    public static final long CLOSE_OFF_ID = 50;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Use unique name of RA (i.e. task def) to identify which one this is.
     */
    @Nonnull
    private static Handle toHandle(String serviceRecipientId, long taskDefId) {
        return Handle.fromString(serviceRecipientId + "-" + taskDefId);
    }
    @Nonnull
    public static Handle toHandle(Integer serviceRecipientId, long taskDefId) {
        return Handle.fromString(serviceRecipientId + "-" + taskDefId);
    }

    /**
     * Use unique name of RA (i.e. task def) and unique task id for a particular task instance.
     */
    @Nonnull
    private static Handle toHandle(int serviceRecipientId, long taskDefId, UUID taskInstanceUuid) {
        if (taskInstanceUuid == null) {
            return toHandle(serviceRecipientId, taskDefId);
        }
        return Handle.fromString(serviceRecipientId + "-" + taskDefId + "-" + taskInstanceUuid);
    }

    @Nonnull
    private static Integer getServiceRecipientId(Handle handle) {
        return Integer.valueOf(handle.toString().substring(0, handle.toString().indexOf('-')));
    }

    public static long getTaskDefId(Handle handle) {
        var components = StringUtils.split(handle.toString(), "-");
        assert components != null;
        return Long.parseLong(components[1]);
    }

    @Nullable
    public static UUID getTaskInstanceId(Handle handle) {
        var found = StringUtils.ordinalIndexOf(handle.toString(), "-", 2);
        return found == -1
                ? null
                : UUID.fromString(handle.toString().substring(found+1));
    }

    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private ServiceTypeService serviceTypeService;

    private final ServiceRecipientRepository serviceRecipientRepository;

    private final ReferralRepository referralRepository;

    private final TaskStatusRepository taskStatusRepository;

    private final UserRepository userRepository;


    @Override
    public boolean activitiWorkflowEnabledFor(String processKey, String instanceKey) {
        throw new UnsupportedOperationException("Must only call this on ActivitiWorkflowService");
    }

    @Override
    public WorkflowInstance instantiateWorkflow(String processKey, String instanceKey) {
        throw new UnsupportedOperationException("Must only call this on ActivitiWorkflowService");
    }

    @Override
    public List<WorkflowTask> getWorkflowTasks(WorkflowInstance instance) {
        throw new UnsupportedOperationException("TODO");
    }

    /**
     *
     * @param processDefKey the key identifying the workflow - always "linear-workflow" for this impl
     * @param serviceRecipientIdStr the id as a string
     */
    @Override
    public List<WorkflowTask> getWorkflowTasks(String processDefKey, String serviceRecipientIdStr) {

        log.info("getWorkflowTasks({}, serviceRecipientId={})", processDefKey, serviceRecipientIdStr);
        var serviceRecipientId = Integer.parseInt(serviceRecipientIdStr);

        Integer serviceTypeId = Integer.valueOf(processDefKey.substring(LINEAR_WORKFLOW_PROCESS_KEY.length()));
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(serviceTypeId);
        List<TaskDefinitionEntryViewModel> orderedTaskDefs = serviceType.taskDefinitionEntries;

        ServiceRecipientSummary sr = serviceRecipientRepository.findOneSummary(serviceRecipientId);

        int taskDefIndex = getPersistedTaskDefIndex(serviceType, sr);
        taskDefIndex = ensureWizardTaskPointer(serviceRecipientId, serviceType, taskDefIndex);

        List<TaskStatus> tasks = taskStatusRepository.findAllByServiceRecipientId(sr.serviceRecipientId);

        // TODO: need to update serviceRecipient.currentTaskId if this has changed
        List<WorkflowTask> tasksAdapters = new ArrayList<>(orderedTaskDefs.size());

        boolean wasAllowNext = false;

        Map<String, TaskStatusType> forceStatuses = getStatusesToForce(serviceType, sr, orderedTaskDefs);

        for (TaskDefinitionEntryViewModel workflowTask : orderedTaskDefs) {

            boolean isWizardTask = serviceType.isWizardTask(workflowTask.name);

            TaskStatusType forceStatus = forceStatuses.getOrDefault(workflowTask.name, wasAllowNext ? TaskStatusType.AVAILABLE : null);

            WorkflowTask adaptedTask = new AdapterFromOptionalTask(serviceRecipientIdStr, processDefKey, isWizardTask, taskDefIndex, tasks, forceStatus)
                    .apply(workflowTask);
            tasksAdapters.add(adaptedTask);

            assert adaptedTask != null;
            wasAllowNext = adaptedTask.isAvailable() && workflowTask.allowNext;
        }
        return tasksAdapters;
    }

    private Map<String, TaskStatusType> getStatusesToForce(ServiceTypeViewModel serviceTypeViewModel, ServiceRecipientSummary sr, List<TaskDefinitionEntryViewModel> orderedTaskDefs) {

        var forced = new HashMap<String, TaskStatusType>();

        Function<String, Boolean> obeyAllowNextFinder = taskName -> {
            var entry = serviceTypeViewModel.taskDefinitionEntries.stream()
                    .filter(t -> taskName.equalsIgnoreCase(t.name))
                    .findFirst()
                    .map(t -> "y".equals(t.settings.get("obeyAllowNext")));
            return entry.orElse(false);
        };

        var base = serviceRecipientRepository.findOne(sr.serviceRecipientId);
        if (base.getPrefix().equals(ReferralServiceRecipient.PREFIX)) {
            // serviceRecipientRepository doesn't provide the getReferral or parentId
            //      ((ReferralServiceRecipient) base).getReferral();
            //      entityManager.find(Referral.class, sr.parentId);
            var r = referralRepository.findOneReferralSummaryByServiceRecipientId(sr.serviceRecipientId);
            assert r != null;

            // close is only available if accepted
            forced.put(Task.Names.CLOSE, r.isAcceptedOnService() ? TaskStatusType.AVAILABLE : TaskStatusType.DISABLED);

            // if obeyAllowNext is set, then do not force - leave to allowNext logic
            if (r.isAcceptedOnService() && obeyAllowNextFinder.apply(Task.Names.CLOSE)) {
                forced.remove(Task.Names.CLOSE);
            }

            /*
            // disable tasks after a rejection point
            if (r.isSignposted()) {
                AcceptState[] states = r.acceptedStates();
                // NB this does mean nothing is selectable after if previously filled in
                var disableAfterTaskName = (states[0] == AcceptState.SIGNPOSTED) // appropriate referral stage
                    ? orderedTaskDefs.stream().filter(t -> t.name.equalsIgnoreCase(Task.Names.ACCEPT_ON_SERVICE))
                    : orderedTaskDefs.stream().filter(t -> t.name.equalsIgnoreCase(Task.Names.ACCEPT_ON_SERVICE));
                } else {
                    // disable after 'accept on service'
                }
            }
            */
        }

        // if obeyAllowNextFinder, do nothing, otherwise force available
        if (!obeyAllowNextFinder.apply(Task.Names.ACCEPT_ON_SERVICE)) {
            forced.put(Task.Names.ACCEPT_ON_SERVICE, TaskStatusType.AVAILABLE);
        }

        return forced;
    }

    @Override
    public List<WorkflowTask> getFilteredWorkflowTasks(String processKey, String instanceKey, String filterVariableKey,
                                                       String tasksToShow) {
        // TODO: For now we just return the whole lot to avoid breaking existing code
        return getWorkflowTasks(processKey, instanceKey);
    }

    @Override
    public Map<String, Object> getProcessInstanceVariables(String workflowKey, String instanceKey) {
        throw new UnsupportedOperationException("TODO");
    }

    @Override
    public WorkflowTask findActiveWorkflowTask(Handle handle) {
        Integer serviceRecipientId = getServiceRecipientId(handle);
        ServiceRecipientSummary serviceRecipient = serviceRecipientRepository.findOneSummary(serviceRecipientId);
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(Math.toIntExact(serviceRecipient.loadConfigServiceTypeId()));

        TaskDefinitionEntryViewModel taskDefinition = serviceType.getTaskDefById(getTaskDefId(handle));
        List<TaskStatus> tasks = taskStatusRepository.findAllByServiceRecipientIdAndTaskDefinitionId(serviceRecipientId, taskDefinition.taskDefId);
        return new AdapterFromOptionalTask(serviceRecipientId,
                serviceType.processKey,
                serviceType.isWizardTask(taskDefinition.name),
                getPersistedTaskDefIndex(serviceType, serviceRecipient),
                tasks)
                .apply(taskDefinition);
    }

    @Override
    public WorkflowTask getActiveOrHistoricalWorkflowTask(Handle taskHandle) {
        return getTaskInstanceId(taskHandle) == null
            ? findActiveWorkflowTask(taskHandle)
            : new AdapterFromPersistedTask(serviceTypeService).apply(
                    taskStatusRepository.getOne(Objects.requireNonNull(getTaskInstanceId(taskHandle))));
    }

    @Override
    public void completeWorkflowTask(WorkflowTask taskCompleted) {
        int serviceRecipientId = getServiceRecipientId(taskCompleted.getHandle());

        TaskStatus taskStatus = getTaskStatus(taskCompleted.getHandle());
        if (taskStatus != null) {
            taskStatus.setCompleted(Instant.now());
            taskStatus.setCompletedStatus(entityManager.getReference(ListDefinitionEntry.class, ListDefinitionEntry.TASKSTATUS_COMPLETED_ID));
            taskStatusRepository.save(taskStatus);
        }
        ensureRepeatTask(serviceRecipientId, getTaskDefId(taskCompleted.getHandle()));
        ensureNextTasks(serviceRecipientId, getTaskDefId(taskCompleted.getHandle()));
    }

    /**
     * Create this task again, if required, because of dueDateSchedule.
     * First, get the earliestCreated (or now), so we can start a schedule from some baseline and calculate the schedule position.
     *      NB It may be that users expect to go from the first targetDate instead, but we've not done that.
     * Then get the afterDate (the latestDueDate or now) which calculates the schedule until after this date.
     * Then get the lastCompletedDate to calculate a resetDate for any repeated schedule, such that when the schedule
     * calculates its date past the lastCompletedDate, it 'resets' the current schedule counter from the lastCompletedDate.
     */
    private void ensureRepeatTask(int serviceRecipientId, long taskDefId) {
        ServiceRecipientSummary serviceRecipient = serviceRecipientRepository.findOneSummary(serviceRecipientId);
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(serviceRecipient.loadConfigServiceTypeId());

        var nowJoda = DateTime.now(DateTimeZone.UTC);
        var nowJdk = JodaToJDKAdapters.dateTimeToJdk(nowJoda);

        // if the task exists and has a dueDateSchedule
        if (serviceType.hasTaskDefById(taskDefId) && serviceType.getTaskDefById(taskDefId).dueDateSchedule != null) {

            // find the earliest created date to calculate the 'original earliestDate'
            var earliestTask = taskStatusRepository.findFirstByServiceRecipientIdAndTaskDefinitionIdOrderByCreatedAsc(serviceRecipientId, taskDefId);
            var earliestCreated = earliestTask.isPresent() ? earliestTask.get().getCreated() : nowJoda;
            var earliestDate = JodaToJDKAdapters.dateTimeToJdk(earliestCreated).toLocalDate();

            // find the latest due date to determine afterDate
            var afterDate = nowJdk.toLocalDate();
            // find the latest completed date to determine the schedule resetDate - see calculateAllScheduleDates
            LocalDate latestCompletedDate = null;

            // NB if there isn't an earliest task then there won't be a latest due or completed
            if (earliestTask.isPresent()) {

                // find the latest completed date to determine the schedule resetDate - see calculateAllScheduleDates
                var latestCompletedTask = taskStatusRepository.findFirstByServiceRecipientIdAndTaskDefinitionIdAndCompletedIsNotNullOrderByCompletedDesc(serviceRecipientId, taskDefId);
                latestCompletedDate = latestCompletedTask.map(t -> t.getCompleted().atZone(EccoTimeUtils.UTC).toLocalDate()).orElse(null);

                // find the latest due date to determine afterDate
                // we have to be after the latest due date, else what's the point in the next timer
                var latestDueTask = taskStatusRepository.findFirstByServiceRecipientIdAndTaskDefinitionIdOrderByDueDateDesc(serviceRecipientId, taskDefId);
                // if no dueDates, stick with today
                // test against today, as we don't want to be dealing with historical due dates
                var latestDueDate = latestDueTask.get().getDueDate();
                if (latestDueDate != null && afterDate.isBefore(latestDueDate.toLocalDate())) {
                    afterDate = latestDueDate.toLocalDate();
                }

                // the new requirement is to be after the completed date,
                // but since completed can only ever be 'now' it was likely the latestDueDate > today (above) that was needed
                if (latestCompletedDate != null && afterDate.isBefore(latestCompletedDate)) {
                    afterDate = latestCompletedDate;
                }

                // if afterDate > nowDate, we surely want to just return - there is a latestDueTask after today for this taskDefId
                // this is helpful when we might be completing a previous task
                // however, it doens't handle tasks completing early - where afterDate is in the future
                //if (afterDate.isAfter(nowDate)) {
                //    return;
                //}
            }

            var schedule = serviceType.getTaskDefById(taskDefId).dueDateSchedule;
            var nextDueDate = TaskStatus.calculateNextDueDate(schedule, earliestDate, latestCompletedDate, afterDate);

            // if the schedule has another after the latest schedule and after today...
            if (nextDueDate != null) {
                createTask(serviceRecipientId, taskDefId, nextDueDate, determineBestUserForTask(serviceRecipient));
            }
        }

    }

    /**
     * Create the next task, and the next tasks with allowNext
     * @param completedTaskDefId The task definition that has just been completed
     * @param serviceRecipientId The service recipient who has this task
     * @return The next immediate task index
     */
    private int ensureNextTasks(int serviceRecipientId, long completedTaskDefId) {
        ServiceRecipientSummary serviceRecipient = serviceRecipientRepository.findOneSummary(serviceRecipientId);

        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(serviceRecipient.loadConfigServiceTypeId());
        int previousTaskDefIndex = getPersistedTaskDefIndex(serviceType, serviceRecipient);
        // we can't trust that the taskDefId still exists in the config
        int nextTaskDefIndex = serviceType.hasTaskDefById(completedTaskDefId)
                ? serviceType.getTaskDefById(completedTaskDefId).taskDefIndex + 1
                : previousTaskDefIndex + 1;

        // protect against old code setting index to v large value to mean "all done"
        if (nextTaskDefIndex >= serviceType.taskDefinitionEntries.size()) {
            return previousTaskDefIndex;
        }

        if (nextTaskDefIndex > serviceRecipient.currentTaskIndex) {
            var nextTaskDef = serviceType.taskDefinitionEntries.get(nextTaskDefIndex);
            User user = determineBestUserForTask(serviceRecipient);
            ensureTask(serviceRecipientId, nextTaskDef.name, nextTaskDef.taskDefId, nextTaskDef.dueDateSchedule, user);

            serviceRecipientRepository.updateTaskDefId(serviceRecipientId, nextTaskDef.taskDefId);
            serviceRecipientRepository.updateTaskDefIndex(serviceRecipientId, nextTaskDefIndex);

            var remainingTaskDefs = serviceType.taskDefinitionEntries.stream()
                    .filter(td -> td.taskDefIndex > nextTaskDefIndex)
                    .toList();

            // create remaining tasks
            var lastAllowNext = nextTaskDef.allowNext;
            for (TaskDefinitionEntryViewModel remainingTaskDef : remainingTaskDefs) {
                // if nextTaskDef has allowNext, or we are on a run of allowNext
                if (lastAllowNext) {
                    lastAllowNext = remainingTaskDef.allowNext;
                    ensureTask(serviceRecipientId, remainingTaskDef.name, remainingTaskDef.taskDefId, remainingTaskDef.dueDateSchedule, user);
                } else {
                    break;
                }
            }

            return nextTaskDefIndex;
        } else {

            return serviceRecipient.currentTaskIndex;
        }
    }

    private User determineBestUserForTask(ServiceRecipientSummary serviceRecipient) {
        // just assign the worker, if any
        //  OR who is 'less busy' algorithm etc
        //  OR the current user: return SecurityUtil.getAuthenticatedUser();
        //  OR no-one, until we pick it up
        if (serviceRecipient.discriminator.equals(ReferralServiceRecipient.DISCRIMINATOR)) {
            var r = referralRepository.findByServiceRecipient_Id(serviceRecipient.serviceRecipientId);
            var workerIndividual = r.getSupportWorker();
            if (workerIndividual != null) {
                return userRepository.findByContactId(workerIndividual.getId());
            }
        }
        return null;
    }

    /**
     * Create a task for the first time, if required.
     * @param taskDefId The task id of the task to create
     * @param dueDateSchedule The schedule from which to create the due date (assumed to be integer days)
     */
    private void ensureTask(int serviceRecipientId, String taskName, long taskDefId, String dueDateSchedule, User user) {
        // for now, don't create a close off task
        if (CLOSE_OFF.equalsIgnoreCase(taskName)) {
            return;
        }
        TaskStatus task = getTaskStatus(serviceRecipientId, taskDefId);
        if (task == null) {

            // since its the first of the task, use today as the startDate
            var dueDate = TaskStatus.calculateNextDueDate(dueDateSchedule, LocalDate.now());
            // lets only assign the user if there is a timer
            createTask(serviceRecipientId, taskDefId, dueDate, dueDate != null ? user : null);
        }
    }

    private void createTask(int serviceRecipientId, long taskDefId, LocalDateTime dueDate, User user) {
        var task = TaskStatus.builder()
                .serviceRecipientId(serviceRecipientId)
                .taskDefinitionId(taskDefId)
                .dueDate(dueDate != null ? dueDate.toLocalDate().atStartOfDay(ZoneId.of("UTC")).toLocalDateTime() : null)
                .assignedUser(user)
                .build();
        taskStatusRepository.save(task);
        taskStatusRepository.flush();
    }

    @Override
    public void ensureUnplannedWorkflowTask(Handle handle) {
        ensureLinearTask(handle);
    }

    private TaskStatus ensureLinearTask(Handle handle) {
        TaskStatus task = getTaskStatus(handle);
        if (task == null) {
            task = TaskStatus.builder()
                    .serviceRecipientId(getServiceRecipientId(handle))
                    .taskDefinitionId(getTaskDefId(handle))
                    .build();
        }
        return taskStatusRepository.save(task);
    }

    @Override
    public void claimTask(Handle handle, String username) {
        var task = ensureLinearTask(handle);
        task.setAssignedUser(getUser(username));
        taskStatusRepository.save(task);
    }

    /**
     * When asking for TaskStatus for a referral aspect, we return the latest if there are more than one as there
     * should be only one that is incomplete.
     * NB We are now provided the taskInstanceUuid in the handle (see toHandle) which we could use here instead of calculating the latest.
     */
    private TaskStatus getTaskStatus(Handle handle) {
        return getTaskStatus(getServiceRecipientId(handle), getTaskDefId(handle));
    }

    @Nullable
    private TaskStatus getTaskStatus(Integer serviceRecipientId, long taskDefId) {
        List<TaskStatus> tasks = taskStatusRepository.findAllByServiceRecipientIdAndTaskDefinitionId(serviceRecipientId, taskDefId);
        if (tasks.isEmpty()) {
            return null;
        }
        return getLatest(tasks);
    }

    private TaskStatus getLatest(List<TaskStatus> tasks) {
        if (tasks == null) {
            return null;
        }
        return tasks.stream()
            .max(
                comparing(TaskStatus::getCreated)
                    // a historical error meant the client code was calling markCompleted (as well as the server side)
                    // which completed the new task and created another one - possibly with identical created times
                    // so we get the null-first completed where tasks are created the same
                    // this doesn't fix the data (tasks will appear to skip a due date), but it does make things consistent
                    // NB this is comparing null on the property values, and although we only expect one null, completed desc makes sense
                    .thenComparing(TaskStatus::getCompleted, nullsFirst(reverseOrder()))
                    // NB this is comparing null taskStatus and not comparing null completed
                    //.thenComparing(Comparator.nullsFirst(comparing(TaskStatus::getCompleted)))
            ).orElse(null);
    }

    private User getUser(String username) {
        return userRepository.getByUsername(username);
    }

    @Override
    public void delegateTask(Handle handle, String username) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public WorkflowTask createAdhocWorkflowTaskForUser(WorkflowTaskCreateTemplate template, String username) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public WorkflowTask createAdhocWorkflowTaskForGroup(WorkflowTaskCreateTemplate template, String candidateGroup) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public Collection<WorkflowTaskSummary> getActiveWorkflowTaskSummary() {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public List<WorkflowTask> getAvailableWorkflowTasks(String username) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public List<WorkflowTask> getAssignedWorkflowTasks(String username) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public List<WorkflowTask> getAvailableWorkflowTasksForGroups(List<String> candidateGroups) {
        throw new UnsupportedOperationException("Only available for Activiti");
    }

    @Override
    public WorkflowTaskDefinition getWorkflowTaskDefinition(TaskDefinitionHandle handle) {
        throw new UnsupportedOperationException("Not impl"); // Impl for Activiti but not actually used at present
    }

    @Override
    public Map<String, WorkflowTask> getWorkflowTasksAsMap(List<WorkflowTask> tasks) {
        throw new UnsupportedOperationException("TODO"); // Currently only used for Activiti impl and only from webflow code
    }

    @Override
    public void deleteWorkflowInstance(com.ecco.workflow.WorkflowInstance.Handle instance, String reason) {
        throw new UnsupportedOperationException("TODO");
    }

    private int getPersistedTaskDefIndex(ServiceTypeViewModel serviceType, ServiceRecipientSummary sr) {
        if (sr.currentTaskDefId == null) {
            log.warn("No currentTaskId set for sr:{}", sr.serviceRecipientId);
            // TODO if currentTaskId is null get the currentTaskDefinitionIndex ... we could push this in to BaseServiceRecipient actually
            return sr.currentTaskIndex;
        }

        try {
            int taskDefIndex = serviceType.getTaskDefById(sr.currentTaskDefId).taskDefIndex;
            return Math.max(taskDefIndex, sr.currentTaskIndex);
        } catch (IllegalArgumentException e) {
            return sr.currentTaskIndex;
        }
    }

    @Nonnull
    private Predicate<ServiceType_TaskDefinition> matchesId(Long currentTaskId) {
        return stra -> Objects.equals(stra.getTaskDefinition().getId(), currentTaskId);
    }

    private int ensureWizardTaskPointer(int serviceRecipientId, ServiceTypeViewModel serviceType, int currentTaskDefIndex) {
        var minimumTaskDef = serviceType.getTaskDefByNameStrict("referralView");
        int minTaskDefIndex = minimumTaskDef.taskDefIndex + 1;

        if (currentTaskDefIndex >= minTaskDefIndex) {
            // already beyond referralView task
            // the assumption here is that tasks have already been handled
            return currentTaskDefIndex;
        } else {
            // TODO its surely a wrong config to have nothing after referralView - although we could just relax this and return currentTaskDefIndex
            if (minTaskDefIndex >= serviceType.taskDefinitionEntries.size()) {
                throw new IndexOutOfBoundsException("There is no task after referralView task to set as the current task (e.g. close then endFlow)");
            }
            return ensureNextTasks(serviceRecipientId, serviceType.taskDefinitionEntries.get(minTaskDefIndex - 1).taskDefId);
        }
    }

    /**
     * Adapts an already persisted workflow task.
     */
    private static class AdapterFromPersistedTask implements Function<TaskStatus, WorkflowTask> {

        private ServiceTypeService serviceTypeService;

        public AdapterFromPersistedTask(ServiceTypeService serviceTypeService) {
            this.serviceTypeService = serviceTypeService;
        }

        @Nullable
        @Override
        public WorkflowTask apply(@Nonnull TaskStatus task) {

            return new WorkflowTask() {
                @Override
                public Handle getHandle() {
                    var raId = task.getTaskDefinitionId();
                    if (raId == null) {
                        throw new UnsupportedOperationException("Only workflow tasks are supported");
                    }
                    return toHandle(task.getServiceRecipientId(), raId, task.getId());
                }

                @Override
                public TaskDefinitionHandle getTaskDefinitionHandle() {
                    var serviceTypeId = task.getServiceRecipient().loadConfigServiceTypeId();
                    var serviceTypeDto = serviceTypeService.findOneDto(serviceTypeId);
                    // in a workflow item, taskDefinitionId will exist
                    var taskDefId = task.getTaskDefinitionId();
                    assert taskDefId != null;
                    var ra = serviceTypeDto.taskDefinitionEntries.stream()
                            .filter(td -> td.taskDefId == taskDefId).findFirst().get();
                    return TaskDefinitionHandle.from(serviceTypeDto.processKey, task.getTaskDefinition().getName()); // or should we use id
                }

                @Override
                public WorkflowInstance getWorkflowInstance() {
                    return null;
                }

                @Override
                public String getName() {
                    return task.getTaskDefinition().getName();
                }

                @Override
                public String getAssignee() {
                    return task.getAssignedUser() == null ? null
                            : task.getAssignedUser().getUsername();
                }

                @Override
                public Optional<LocalDateTime> getDueDate() {
                    return Optional.ofNullable(task.getDueDate());
                }

                @Override
                public Optional<Instant> getEndTime() {
                    return Optional.ofNullable(task.getCompleted());
                }

                @Override
                public boolean isAvailable() {
                    return !isCompleted() && task.getCompletedStatus() == null;
                }

                @Override
                public boolean isCompleted() {
                    return task.getCompleted() != null;
                }

                @Override
                public URI getImplementationUri() {
                    return null;
                }

                @Override
                public Collection<WorkflowLinkedResource> getLinkedResources() {
                    return null;
                }
            };
        }
    }

    enum TaskStatusType { DISABLED, AVAILABLE }

    private class AdapterFromOptionalTask implements Function<TaskDefinitionEntryViewModel, WorkflowTask> {
        private final int currentTaskOrderIndex;
        private final String processDefKey;
        private final String serviceRecipientId;
        private final boolean wizardTask;
        private final Map<Long, List<TaskStatus>> tasks;
        private final TaskStatusType forceStatus;

        /**
         * @param currentTaskOrderIndex orderby value for the task that is current - this is calculated on the fly from
         *                              currentTaskId before calling here, so that we adapt to inserted tasks in workflow.
         */
        AdapterFromOptionalTask(String serviceRecipientId, String processDefKey, boolean isWizardTask,
                                int currentTaskOrderIndex, List<TaskStatus> tasks, TaskStatusType forceStatus) {
            this.serviceRecipientId = serviceRecipientId;
            this.processDefKey = processDefKey;
            this.currentTaskOrderIndex = currentTaskOrderIndex;
            this.wizardTask = isWizardTask;
            this.tasks = tasks.stream()
                    .filter(t -> t.getTaskDefinition() != null)
                    .collect(groupingBy(TaskStatus::getTaskDefinitionId));
            this.forceStatus = forceStatus;
        }

        AdapterFromOptionalTask(Integer serviceRecipientId, String workflowProcessKey, boolean isWizardTask, int currentTaskOrderIndex, List<TaskStatus> tasks) {
            this(String.valueOf(serviceRecipientId), workflowProcessKey, isWizardTask, currentTaskOrderIndex, tasks, null); // TODO: Review / test isAvailable behaviour via this path
        }


        @Nullable
        @Override
        public WorkflowTask apply(@Nonnull TaskDefinitionEntryViewModel stra) {
            TaskStatus task = getLatest(tasks.get(stra.taskDefId));
            var instance = task != null ? task.getId() : null;
            return new WorkflowTask() {
                @Override
                public Handle getHandle() {
                    return toHandle(Integer.parseInt(serviceRecipientId), stra.taskDefId, instance);
                }

                @Override
                public TaskDefinitionHandle getTaskDefinitionHandle() {
                    return TaskDefinitionHandle.from(processDefKey, stra.name); // or should we use id
                }

                @Override
                public WorkflowInstance getWorkflowInstance() {
                    return null;
                }

                @Override
                public String getName() {
                    return stra.name;
                }

                @Override
                public String getAssignee() {
                    return task == null ? legacyAssignee()
                            : task.getAssignedUser() == null ? null
                            : task.getAssignedUser().getUsername();
                }

                private String legacyAssignee() {
                    // If we have no task but is available, then we assume legacy
                    //y NB The text here is tied into WorkflowLoader.tsx and TaskRow.tsx.
                    return stra.taskDefIndex < currentTaskOrderIndex ? "possibly complete" : null;
                }

                @Override
                public Optional<LocalDateTime> getDueDate() {
                    return Optional.ofNullable(task == null ? null : task.getDueDate());
                }

                @Override
                public Optional<Instant> getEndTime() {
                    return Optional.ofNullable(task == null ? null : task.getCompleted());
                }

                @Override
                public boolean isAvailable() {
                    if (forceStatus != null) {
                        // if entry exists, then return available or not-available for disabled
                        return forceStatus == TaskStatusType.AVAILABLE;
                    }
                    return stra.taskDefIndex <= currentTaskOrderIndex;
                }

                @Override
                public boolean isCompleted() {
                    // isCompleted logic assumes all tasks inbetween were complete, so we are correcting that.
                    // We could correct by simply looking a the newer 'task' logic, but everything would be incomplete.
                    // To move from the old to the new task pointer we've been using the older task pointer and using that.
                    // Trouble with that is that a pointer far down the task list doesn't mean the tasks inbetween are done.
                    // Pointers are defined as:
                    //      old tasks pointer uses currentTaskOrderIndex
                    //      newer tasks pointer uses currentTaskDefId
                    //      both made compatible by checking currentTaskOrderIndex and currentTaskDefId to see which is the greater - see getCurrentTaskDefIndex()
                    // So we need a way of determining isComplete for non-activiti (aka linear) inbetween tasks that do not have a TaskStatus.
                    // We can't compare stra.taskDefIndex < currentTaskOrderIndex because isComplete would be true for all inbetween tasks.
                    //      NB currentTaskOrderIndex - is the greater of the currentTaskDefId/currentTaskIndex - see getCurrentTaskDefIndex()
                    // We could compare against currentTaskOrderIndex if there were no tasks - but completing one task would suddenly change the logic.
                    // Ideally, we use the audit info to the right of the task - but this logic is client side TaskSummary.tsx / referralView_tasks.jspf
                    // We could see what commands exist to determine that work has been done, and therefore consider the task complete.
                    // However, this still leaves oddities that are best resolved client side, so we leave intermediate tasks incomplete server side.
                    // We could definitively create TaskStatus data (eg database, or on startup) but this is possibly overcomplicated - just let users complete a task.
                    return task == null
                            ? wizardTask
                            : task.getCompleted() != null;
                }

                @Override
                public URI getImplementationUri() {
                    return null;
                }

                @Override
                public Collection<WorkflowLinkedResource> getLinkedResources() {
                    return null;
                }
            };
        }
    }
}
