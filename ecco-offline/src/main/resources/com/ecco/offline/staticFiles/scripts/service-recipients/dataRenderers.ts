import $ = require("jquery");
import _ = require("lodash");

import SessionDataService = require("../feature-config/SessionDataService");
import {ContactsAjaxRepository} from "ecco-dto";
import {getGlobalEccoAPI} from "ecco-components";
import {EditTaskCommand} from "ecco-commands";

let contactsRepository: ContactsAjaxRepository | null = null;

// FIXME: Move this to getGlobalEccoAPI().contactsRepository
export function getContactsRepository(): ContactsAjaxRepository {
    if (!contactsRepository) {
        contactsRepository = new ContactsAjaxRepository(getGlobalEccoAPI().apiClient);
    }
    return contactsRepository;
}

/** Coverts "camelCase_and_then_some" to "camel case and then some" */
// NB this is presumably a clone of ecco-common's StringUtils.camelToSpaces
export function toWords(input: string) {
    return _.snakeCase(input).replace(/_/g, " ");
}

// TODO: Move to ecco-common
export function isPromiseAlike(object: any): object is Promise<any> {
    return object != null && typeof object == "object" && typeof object.then == "function"
}


/** Allow .text() to be called later if we have an async lookup for the value to set */
export function setTextAsyncValue($el: $.JQuery, lookup: string | number | Promise<string | null>) {
    if (isPromiseAlike(lookup)) {
        lookup.then(val => $el.text(val || "-") );
    } else {
        $el.text(lookup);
    }
}

/** Allow .text() to be called later if we have an async lookup for the value to set */
export function setTextAsyncArray($el: $.JQuery, lookup: string[] | number[] | Promise<string>[]) {
    if (isPromiseAlike(lookup[0])) {
        Promise.all(lookup as Promise<string>[]).then(val => $el.text(val.join(", ")) );
    } else {
        $el.text(lookup.join(", "));
    }
}

export function nullAsEmpty(str: string | null) {
    return str ? str : "";
}

export function asYesNo(bool: boolean) {
    return (bool === null || bool === undefined) ? "unset" :
        bool ? "yes" : "no";
}

export function asAgreedRejected(bool: boolean) {
    return (bool === null || bool === undefined) ? "unset" :
        bool ? "agreed" : "rejected";
}

export function summariseAgency(id: number): Promise<string> | string | null {
    return !id ? null : getContactsRepository().findOneAgency(id)
        .then(contact => contact.companyName);
}

export function summariseAppointmentType(id: number): Promise<string> | null {
    return !id || id == -1 ? null : SessionDataService.getFeatures()
        .then(data => data.getAppointmentTypeById(id).name);
}

// historical ids can now be different - commentType was its own command, but now listdefs
// we may just need to show the id (as per 'DEV-1618 Switch flag lookups for audits to ids')
// or we can supply a date as keyvalue to a setting with namespace 'com.ecco.migration' and keyname 'commenttypes'
// such that a lookup with date of command can find the right pre-migrationId across the listdefs table
export function summariseCommentType(id: number): Promise<string> | null {
    return !id || id == -1  ? null : SessionDataService.getFeatures()
        .then(data => data.getListDefinitionEntryById(id).getDisplayName());
}

export function summariseIndividual(id: number | null): Promise<string> | string | null {
    return !id ? null : getContactsRepository().findOneIndividual(id)
        .then(contact => contact
            ? nullAsEmpty(contact.firstName) + " " + (contact.lastName || contact.contactId)
            : `[Missing contact: id=${id}]`
        );
}

export function summariseListDef(id: number | null): Promise<string | null> {
    return SessionDataService.getFeatures()
        .then(sessionData => id && id != -1 // HACK: we have accidentally had -1 in the database .. it should've been null
            ? sessionData.getListDefinitionEntryById(id).getFullName()
            : null);
}

export function summariseTaskHandle(taskHandle: string, svcAllocId: number): Promise<string | null> {
    return SessionDataService.getFeatures()
        .then(sessionData => {
            if (EditTaskCommand.isLinearTaskHandle(taskHandle)) {
                const linearTaskHandle = EditTaskCommand.fromLinearTaskHandle(taskHandle)
                const st = sessionData.getServiceTypeByServiceCategorisationId(svcAllocId)
                const taskName = sessionData.getTaskDefinitionById(linearTaskHandle.taskDefId)
                return sessionData.lookupTaskName(linearTaskHandle.taskDefId, st?.id, taskName?.name)
            }
            return "";
        });
}

export function summariseFundingSource(id: number | null): Promise<string | null> {
    return SessionDataService.getFeatures().then(sessionData => {
        return id ? sessionData.getFundingSourceNameById(id) : null;
    });
}

export function summariseProject(id: number | null): Promise<string | null> {
    return SessionDataService.getFeatures().then(sessionData => {
        return !id ? null
            : id == -1 ? "can access all projects within selected services"
            : sessionData.getProject(id)?.name ?? `[Missing project: id=${id}]`;
    });
}
export function summariseResidence(id: number | null): Promise<string | null> {
    return getGlobalEccoAPI().getBuildingRepository().getCachedBuildingsMap().then(buildings => {
        if (!id) { return null; }
        const residence = buildings[id];
        return (residence.parentId ? buildings[residence.parentId].name + " : " : "") + residence.name;
    });
}
export function summariseService(id: number | null): Promise<string | null> {
    return SessionDataService.getFeatures().then(sessionData => {
        return id ? sessionData.getService(id).name : null;
    });
}
export function summariseServiceType(idAsString: number | null): Promise<string | null> {
    if (!idAsString) {
        return Promise.resolve("unset");
    }
    return SessionDataService.getFeatures().then(sessionData => {
        // the actual audit has a string for this, which causes the runtime to pass through as a string
        const id = Number(idAsString)
        return sessionData.getServiceTypeById(id).getName()
    });
}
