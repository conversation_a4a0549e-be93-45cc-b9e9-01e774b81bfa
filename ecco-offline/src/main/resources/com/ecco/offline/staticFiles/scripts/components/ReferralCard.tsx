import * as React from "react";

import {Card, CardActions, CardHeader} from "@eccosolutions/ecco-mui";
import {ReferralDto as Referral} from "ecco-dto";
import {applicationRootPath} from "application-properties";
import {Navigation, useServicesContext, withNavigation, WithNavigationProps} from "ecco-components";
import {textColumn} from "../controls/tableSupport";
import {QnAnswerWorkWithRefToReferralAggregate} from "../reports/analysis/types";


// also see ClientReferralsPopup#openReferral
function linkTo(navigation: Navigation, referral: Referral) {
    const srId = referral.serviceRecipientId;
    const url = `${applicationRootPath}r/app/referrals/${srId}/`;
    return <a key={referral.referralId} className="btn btn-link"
                                              onClick={navigation.asCallback(`/referrals/${srId}/`)}
                                              href={navigation.asHref(url)}
        >referral</a>;
}

interface CardProps {
    showDate?: boolean;
    referral: Referral;
}

function ReferralCard(props: CardProps & WithNavigationProps) {
    const {sessionData} = useServicesContext()
    const referral = props.referral;
    const service = sessionData.getServiceCategorisation(props.referral.serviceAllocationId).serviceName;
    return (
        <Card style={{marginTop: 2, marginBottom: 2}}>
            <CardHeader
                title={referral.clientDisplayName}
                subheader={`${service} (${sessionData.getDto().messages[referral.statusMessageKey]})`}
            />
            <CardActions>
                {linkTo(props.navigation, referral)}
            </CardActions>
        </Card>
    );
}

export = withNavigation(ReferralCard);
