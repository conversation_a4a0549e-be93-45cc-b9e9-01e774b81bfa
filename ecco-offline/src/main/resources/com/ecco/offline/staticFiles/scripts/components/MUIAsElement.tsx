import {AsyncSessionData, componentAsElement, EccoTheme, LoadingSpinner} from "ecco-components";
import React, {ReactElement, Suspense} from "react";
import SessionDataService from "../feature-config/SessionDataService";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import {SessionData, SessionDataDto} from "ecco-dto";

export function publicComponentAsElementForMui<P>(
    reactElement: ReactElement<P>,
    elementId: string,
    spinner?: ReactElement
): Element {
    return componentAsElementForMui(reactElement, elementId, spinner, true);
}

export function componentAsElementForMui<P>(
    reactElement: ReactElement<P>,
    elementId: string,
    spinner?: ReactElement,
    globalOnly?: boolean
): Element {

    const p = globalOnly
            ? () => SessionDataService.getGlobal().then(s => new SessionData(s.getDto() as SessionDataDto))
            : () => SessionDataService.getFeatures();
    return componentAsElement(
        <AsyncSessionData promiseFn={p}>
            <ServicesContextProvider spinner={spinner}>
                <EccoTheme prefix="muiAs">
                    <div className="m-ui">
                        <Suspense fallback={<LoadingSpinner/>}>
                            {reactElement}
                        </Suspense>
                    </div>
                </EccoTheme>
            </ServicesContextProvider>
        </AsyncSessionData>,
        elementId
    );
}