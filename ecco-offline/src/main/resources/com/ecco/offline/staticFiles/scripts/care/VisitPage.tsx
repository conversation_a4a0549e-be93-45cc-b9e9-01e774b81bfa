import {EventCard} from "../components/EventCard";
import {
    CardData,
    CardsContainer,
    PrintableAppointments,
    useAppBarOptions,
    useServicesContext,
    useCareVisitMenuContext,
    CareVisitMenu, UserMenu, SearchForm, CardSource
} from "ecco-components";
import * as React from "react";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import {Box, Button, ButtonGroup, Grid, Typography} from '@eccosolutions/ecco-mui';
import {CareOrEventCard} from "./CareOrEventCard";
import {CareShiftEventCard} from "./CareShiftEventCard";
import {EccoDate} from "@eccosolutions/ecco-common";
import {FC, useState, useMemo} from "react";
import {CareOrEventCardData} from "./CareOrEventCardData";


/**
 * CareOrEventCard is shown from the 'sources' below
 */
function componentFactory(card: CardData, showEventAsVisit?: boolean) {

    if (card instanceof CareOrEventCardData) {
        // NB evidence is saved on a 'start visit'
        return <Grid item key={card.dto.uid} xs={card.isCareRun() ? 12 : undefined}>
            {card.isCareRun()
                    ? <CareShiftEventCard card={card}/>
                    : <CareOrEventCard event={card.dto} showEventAsVisit={showEventAsVisit}/>
            }
        </Grid>;
    }
    const dto = (card as CareOrEventCardData).dto;
    return <EventCard event={dto} key={dto.uid}/>;
}

type Mode = "visits" | "schedule";
const Actions: FC<{ mode: Mode, setMode: (mode: Mode) => void }> = ({mode, setMode}) => <>
    <Grid item xs={6}>
        <ButtonGroup>
            <Button
                    onClick={() => setMode("visits")}
                    style={mode == "visits" ? {textDecoration: "underline"} : undefined}
            >visits</Button>
            <Button
                    onClick={() => setMode("schedule")}
                    style={mode == "schedule" ? {textDecoration: "underline"} : undefined}
            >plan</Button>
        </ButtonGroup>
    </Grid>
    <Grid item xs/>
</>;


/** NB Can be called with a specific date /nav/r/care/2023-04-19 - see 076daf58 */
export const VisitPage: FC<{date?: string, source: CardSource & {setDate(dte: EccoDate): void}, showEventsAsVisit?: boolean}> = (props) => {
    const [mode, setMode] = useState<Mode>("visits")
    const sources = useMemo(() => [props.source], []);

    // menu items are set on <AppBarBase right=
    // but CarePage is available from CareAppBar and WelcomeAppBar
    // so we apply the menu items to the context, which is picked up in WelcomeAppBar right
    useAppBarOptions(<Actions key="header-actions" mode={mode} setMode={setMode}/>, [mode], undefined, [<CareVisitMenu/>, <UserMenu/>])

    const {sessionData} = useServicesContext();
    const ctxMenu = useCareVisitMenuContext();

    if (props.date) {
        // date can actually be a srId, so lets check it is a date first
        const dte = EccoDate.parseIso8601(props.date);
        if (dte) {
            props.source.setDate(dte);
            // const setDateExists = props.source.hasOwnProperty("setDate");
            // if (setDateExists) {
            //     props.source["setDate"] = dte;
            // }
        }
    }

    if (!sessionData || !ctxMenu) return <div>...</div>

    return (
            <ServicesContextProvider>
                <div className="container-fluid top-gap-15">
                    {mode == "visits" ?
                            /* visits */
                            <CardsContainer
                                    sources={sources} // FIXME: Make this like PrintableAppointments - if possible .. refactor & re-use it with render props for content
                                    componentFactory={card => componentFactory(card, props.showEventsAsVisit)}
                            >
                                <Box m={4}>
                                    <Typography variant="h6">You have no tasks or appointments</Typography>
                                </Box>
                            </CardsContainer>
                            /* plan */
                            : <PrintableAppointments
                                    subjectDisplayName={sessionData.getDto().individualUserSummary.displayName}
                                    calendarId={sessionData.getDto().calendarId}
                            />
                    }
                    {/* NB component is outside AppBar since that uses darkTheme, which gets applied to everything inside */}
                    {ctxMenu.openSearch && (
                            <SearchForm
                                    searchData={ctxMenu.searchData}
                                    onChange={searchData => {
                                        ctxMenu.setSearchData(searchData);
                                    }}
                                    onClose={searchData => {
                                        ctxMenu.setSearchData(searchData);
                                        ctxMenu.setOpenSearch(false);
                                    }}
                            />
                    )}
                </div>
            </ServicesContextProvider>
    );
}
