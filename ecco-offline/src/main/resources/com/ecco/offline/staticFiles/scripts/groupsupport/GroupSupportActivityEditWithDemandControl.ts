import $ = require("jquery");
import BaseControl = require("../controls/BaseControl");
import {ChartControl} from"../reports/charts/ChartControl";
import GroupSupportActivityEditControl = require("./GroupSupportActivityEditControl");
import {ActionsChangedCallback} from "@eccosolutions/ecco-common";

import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";
import * as sdDto from "ecco-dto/session-data/feature-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import DialogContent from "../controls/DialogContent";

/**
 * The idea that we load deman from referrals based on what activities they want
 * UNUSED
 */
class GroupSupportActivityEditWithDemandControl extends BaseControl implements DialogContent {

    public static showInModal(activityId?: number, onSave?: () => void) {
        var control = new GroupSupportActivityEditWithDemandControl(activityId, onSave);
        control.load();
        showFormInModalDom(control);
    }

    private sessionDataRepository = new SessionDataAjaxRepository(apiClient);
    private groupSupportActivityEdit: GroupSupportActivityEditControl;
    private chart: ChartControl<any>;
    private $activityToggle: $.JQuery;
    private $chartToggle: $.JQuery;

    constructor(private activityId?: number, private onSave?: () => void) {
        super($('<div>'));

        this.groupSupportActivityEdit = new GroupSupportActivityEditControl(activityId, onSave);
    }

    private load() {
        this.sessionDataRepository.getSessionDataDto().then((sessionData: sdDto.SessionDataDto) => {
            // enable this feature for group support activity
            if (sessionData.featureSets['global'].featureVotes['groupSupport.new.daysOfWeekReport'].defaultVote == 'ENABLED_BY_DEFAULT') {
                this.chart = new ChartControl("403252ba-0001-babe-babe-dada7ee1600d", undefined, "col-xs-12", null);
            }

            this.render();
            this.groupSupportActivityEdit.load();
            if (this.chart) {
                this.chart.load();
            }
        });
    }

    protected render() {
        this.element().empty();

        if (this.chart) {
            var $section = $('<p>');
            var $buttonToolBar = $('<div class="btn-toolbar">');
            $section.append($buttonToolBar);

            this.$activityToggle = $('<a>edit activity</a>');
            this.$activityToggle.on('click', () => this.toggle('activity'));
            $buttonToolBar.append(this.$activityToggle);

            this.$chartToggle = $('<a>view activity demand</a>');
            this.$chartToggle.on('click', () => this.toggle('chart'));
            $buttonToolBar.append(this.$chartToggle);

            this.element().append($section);
            this.element().append(this.groupSupportActivityEdit.element());
            this.element().append(this.chart.element());

            this.toggle('activity');
        } else {
            this.element().append(this.groupSupportActivityEdit.element());
        }
    }

    private toggle(toggle: string) {
        switch(toggle) {
            case 'activity':
                this.groupSupportActivityEdit.element().show();
                this.chart.element().hide();
                this.$activityToggle.removeClass().addClass('btn btn-primary');
                this.$chartToggle.removeClass().addClass('btn btn-default');
                break;
            case 'chart':
                this.groupSupportActivityEdit.element().hide();
                this.chart.element().show()
                this.$activityToggle.removeClass().addClass('btn btn-default');
                this.$chartToggle.removeClass().addClass('btn btn-primary');
                break;
        }
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback) {
        this.groupSupportActivityEdit.registerActionsChangeListener(updateActions);
    }

    public getTitle() {
        return this.groupSupportActivityEdit.getTitle();
    }

    public getFooter() {
        return this.groupSupportActivityEdit.getFooter();
    }

    public setOnFinished(onFinished: () => void) {
        return this.groupSupportActivityEdit.setOnFinished(onFinished);
    }
}

export = GroupSupportActivityEditWithDemandControl;