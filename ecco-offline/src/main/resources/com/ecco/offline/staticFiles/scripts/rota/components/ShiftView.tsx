import {applicationRootPath} from "application-properties";
import {
    LoadingSpinner,
    ServiceAgreementsView,
    TabsBuilder,
    useBuilding,
    useAppBarOptions,
    useServicesContext
} from "ecco-components";
import * as React from "react";
import {FC} from "react";
import {AuditHistory} from "../../service-recipients/components/AuditHistory";
import {Box} from '@eccosolutions/ecco-mui';

type Props = {
    buildingId: number
};
export const ShiftView: FC<Props> = props => {

    const {buildingId} = props;
    const {building, loading} = useBuilding(buildingId);
    const {sessionData} = useServicesContext();

    useAppBarOptions(`${building?.name ?? '...'}`, [building]);

    if (loading) return <LoadingSpinner/>;

    const Popup = () => <>
        <Box p={2}>
            {new TabsBuilder()
                .addTab("overview",
                    <div className="entityForm">Building:&nbsp;
                        <a href={`${applicationRootPath}nav/service-recipient/${building.serviceRecipientId}`} target={"_blank"}>
                            {building.name}
                        </a>
                    </div>,
                    undefined, "fa-user")
                .addTab("patterns",
                    <ServiceAgreementsView serviceRecipientId={building.serviceRecipientId}/>,
                    undefined, "fa-calendar")
                .addTab( "audit history",
                    <AuditHistory serviceRecipientId={building.serviceRecipientId} sessionData={sessionData}/>,
                    undefined, "fa-history")
                .build()
            }
        </Box>
    </>;

    return <Popup/>;
};
export default ShiftView;
