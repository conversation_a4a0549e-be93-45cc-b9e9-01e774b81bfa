import $ = require("jquery");

import AddGoalForm = require("./AddGoalForm");
import BaseEvidenceForm = require("../BaseEvidenceForm");
import services = require("ecco-offline-data");
import CommandBasedMultiInstanceListControl = require("../../evidence/multi-instance/CommandBasedMultiInstanceListControl");
import SessionData = featureDomain.SessionData;
import {CommandQueue, WorkUuidResolver} from "ecco-commands";
import * as featureDomain from "ecco-dto";
import * as dto from "ecco-dto";
import {ConfigResolver, TaskNames} from "ecco-dto";
import {EvidenceContext, EvidenceDef, SmartStepStatusTransitions} from "ecco-evidence";
import BaseControl from "../../controls/BaseControl";
import ChecklistEvidenceForm from "../checklist/ChecklistEvidenceForm";

/**
 * Used by BuildingOverviewControl to show the 'checks' tab. BaseEvidenceForm is used just for layout and incoming data.
 * Changes are recorded from the modals themselves, not the base form's directly:
 *      'add check' is done in the AddGoalForm with its own commands.
 *      'record check' is done in a modal from CheckInstanceControl.
 * ChecklistEvidenceForm is isolated and doesn't extend BaseEvidenceForm
 */
class ChecksEvidenceForm extends BaseEvidenceForm {

    public static fromBuildingSrId(srId: number) {
        return services.getBuildingRepository().findOneBuildingBySrId(srId).then(bldg => {
            const baseCtrl = new BaseControl();
            const checklistDue = () => {
                ChecklistEvidenceForm.showInModalByIds(srId, "checks due",
                        TaskNames.needsReduction, undefined, true);
            }
            const $rhs = $("<div>").css("text-align", "right")
            baseCtrl.append($rhs.append(
                $("<a>").text("complete due").click(checklistDue)
            ));
            ChecksEvidenceForm.loadAndAttach(srId, baseCtrl.element(), baseCtrl.element());
            return baseCtrl;
        });
    }

    public static loadAndAttach(serviceRecipientId: number, $container: $.JQuery, $footer: $.JQuery) {

        services.getReferralRepository().findOneServiceRecipientWithEntities(serviceRecipientId).then(referral => {
            const evidenceDef = EvidenceDef.fromTaskName(referral.features, referral.configResolver.getServiceType(), "needsReduction"); // TODO: possibly a different mode for repeated checks
            let workUuidQResolver = new WorkUuidResolver();
            const form = new ChecksEvidenceForm(workUuidQResolver, referral, evidenceDef);
            $container.append(form.element());
            $footer.append(form.getFooter());
        });
    }

    constructor(workUuidQResolver: WorkUuidResolver, recipient: dto.ServiceRecipientWithEntities, evidenceDef: EvidenceDef) {
        super(workUuidQResolver, null, null, null, recipient, [], evidenceDef,
            {addFormButtonLabel: "add check"},
            "Checks",
            "col-xs-12", "abs-top-right");
    }

    protected createMainControl() {
        let smartStepStatusTransitions = new SmartStepStatusTransitions(this.evidenceDef.getEvidencePageType(),
            this.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(
                this.evidenceDef.getTaskName(),  "allowActionStraightToAchieve") == "y");

        let context = new EvidenceContext(this.serviceRecipient.serviceRecipientId, this.serviceRecipient,
            this.serviceRecipientActivityTypeInterest,
            this.workUuidResolver, this.evidenceDef,
            smartStepStatusTransitions, this.serviceRecipient.features, this.serviceRecipient.configResolver);
        // we don't need to use/call controlsQ argument
        return new CommandBasedMultiInstanceListControl(context, []);
    }

    protected getAddGoalForm(features: SessionData, configResolver: ConfigResolver) {
        return new AddGoalForm(this.serviceRecipient, EvidenceDef.fromTaskName(features,configResolver.getServiceType(), "needsAssessment"));
    }

    /** Apply the contents of this CommandQueue to this Form by first sending them to the server then applying
     * the contents via super class */
    public applyCommands(commandQueue: CommandQueue): Promise<void> {
        return commandQueue.getCommands()
            .then(cmds => {
                commandQueue.flushCommands()
                    .then(() => super.applyCommandArray(cmds));
        });
    }

    // stubbed - changes are not made from here
    // TODO remove this class from BaseEvidenceForm
    protected isValid(): boolean {
        return true;
    }
}

export = ChecksEvidenceForm;
