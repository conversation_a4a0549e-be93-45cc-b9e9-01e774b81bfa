import BaseAsyncCommandForm = require("../../cmd-queue/BaseAsyncCommandForm");
import CheckboxInput = require("../../controls/CheckboxInput");
import {EccoDate} from "@eccosolutions/ecco-common";
import {ReferralTaskAllocateServiceCommand, ReferralTaskExitCommand} from "ecco-commands";
import * as spDto from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import services = require("ecco-offline-data");
import {SessionData} from "ecco-dto";


class ReferralTaskAllocateControl extends BaseAsyncCommandForm<spDto.ServiceDto[]> {

    public static showInModal(serviceRecipientId: number, onComplete: () => void, taskHandle: string) {
        const form = new ReferralTaskAllocateControl(serviceRecipientId, onComplete, taskHandle);
        showFormInModalDom(form);
        form.load();
    }

    private sessionData: SessionData;
    private serviceId: number;
    private checkboxes = new Array<CheckboxInput>();


    constructor(private serviceRecipientId: number, private onComplete: () => void,
                private taskHandle: string) {
        super("allocate to services");
        this.setSubmitLabel("allocate");
        this.setOnFinished(() => this.loadNextPage());
    }

    protected fetchViewData(): Promise<spDto.ServiceDto[]> {
        return services.getFeatureConfigRepository().getSessionData().then(sd =>
            services.getReferralRepository().findOneReferralSummaryByServiceRecipientIdUsingDto(this.serviceRecipientId)
                .then(sr => {
                    this.sessionData = sd;
                    const svcCat = sd.getServiceCategorisation(sr.serviceAllocationId)
                    this.serviceId = svcCat.serviceId
                    return services.getServiceRepository().findAllServicesProjects();
                })
        );
    }

    protected render(allServices: spDto.ServiceDto[]): void {
        this.element().empty();
        const parentService = allServices.filter((service) => service.id == this.serviceId)[0];
        const allocateToServices = parentService.parameters ? parentService.parameters.allocateToServices : null;

        allServices
            .filter( (service) => allocateToServices ? allocateToServices.indexOf(service.id) > -1 : true)
            .forEach( (service) => {
                const checkbox = new CheckboxInput(service.name, service.id.toString())
                    .change(() => {
                        this.enableSubmit();
                    });
                this.append(checkbox);
                this.checkboxes.push(checkbox);
            });
    }

    protected preSubmitCommandsHook(): void {
        const cmd = new ReferralTaskAllocateServiceCommand(this.serviceRecipientId, this.taskHandle);
        this.checkboxes.forEach( (checkbox) => {
            if (checkbox.isChecked()) {
                // assume a null project - but we used to just choose the first
                var svcCatId = this.sessionData.getServiceCategorisationByIds(parseInt(checkbox.getId()), null);
                cmd.addServiceAllocationId(svcCatId.id)
                //cmd.addService(parseInt(checkbox.getId()));
            }
        });

        this.commandQueue.clear();
        this.commandQueue.addCommand(cmd);

        // close this referral
        const cmdClose = new ReferralTaskExitCommand(this.serviceRecipientId, this.taskHandle);
        cmdClose.changeExitedDate(null, EccoDate.todayLocalTime()); // TODO use a proper from
        this.commandQueue.addCommand(cmdClose);
    }

    private loadNextPage() {
        if (this.onComplete){
            this.onComplete();
        }
    }
}
export = ReferralTaskAllocateControl;
