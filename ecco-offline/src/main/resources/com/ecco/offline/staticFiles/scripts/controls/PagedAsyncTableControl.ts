import $ = require("jquery");

import BaseAsyncTableControl = require("./BaseAsyncTableControl");
import {MenuUpdateEvent} from "../common/events";

var search = new URLSearchParams(window.location.search);


interface HistoryState {
    page: number;
    pageSize: number;
    title: string;
}

abstract class PagedAsyncTableControl<ROW> extends BaseAsyncTableControl<ROW> {

    /** this is 1-indexed to make following url etc easier */
    protected page: number | null
    protected pageSize: number | null
    protected numPages: number;
    protected hasNext: boolean;
    private $menu: $.JQuery;

    constructor(private title: string, cssClasses: string, ignorePageUrl?: boolean) {
        super();
        this.element().addClass(cssClasses);
        window.history && window.addEventListener('popstate', e => this.applyState(e.state)); // FIXME: Invoices tab on referralViewTabs causes issues with this assuming we own the page!

        var pageStr = search.get('page');
        this.page = ignorePageUrl ? 1 : (pageStr ? parseInt(pageStr) : 1);
        var pageSizeStr = search.get('pageSize');
        this.pageSize = pageSizeStr ? parseInt(pageSizeStr) : null; // default to server page size
    }

    protected pushState(page:number, pageSize: number, title: string) {
        let state = {page, title, pageSize};
        if (window.history) {
            const search = new URLSearchParams({page: page.toString()});
            if (pageSize) {
                search.set("pageSize", pageSize.toString());
            }
            const url = `${window.location.pathname}?${search}`;
            window.history.pushState(state, title, url);
        }
        this.applyState(state);
    }

    protected applyState(state: HistoryState) {
        if (!state) return;
        this.page = state.page;
        this.pageSize = state.pageSize;
        this.load();
    }


    protected render(items: ROW[]) {
        this.setMenu();
        super.render(items);

        let $nav = $("<div>").addClass("pagenum text-center").appendTo(this.element());

        if (this.numPages == 0) {
            $nav.append( $("<span>").text(" no results ") );
            return;
        }
        if (this.numPages == 1) {
            return; // don't show any links - just the results
        }

        // If we've asked for a page beyond the end, then we set it so prev page is a valid page, but still
        // show no results (we could re-query)
        if (this.page > this.numPages - 1) {
            this.page = this.numPages;
        }

        // Always show first and last page - this logic should kick in with second page = 2, and penult = 2 when numPages = 3
        let secondPageNum = this.page <= 5 ? 2 : this.page - 3;
        let penultimatePageNum = (secondPageNum + 6 < this.numPages) ? secondPageNum + 6 : this.numPages - 1;

        // Always show first page link
        $nav.append( this.linkToPage(1) );

        // this should execute zero times if we don't have enough pages
        for (let page = secondPageNum; page <= penultimatePageNum; page++) {
            $nav.append( $("<span>").text(" ... ") )
                .append( this.linkToPage(page) );
        }

        if (this.numPages >= 2) {
            $nav.append( $("<span>").text(" ... ") )
                .append( this.linkToPage(this.numPages) );
        }
    }

    protected linkToPage(page: number) {
        let classes = page == this.page ? "btn btn-link disabled" : "btn btn-link";
        return $("<button>").addClass(classes).text(page.toString())
            .click( () => {
                this.pushState(page, this.pageSize, this.title + " (page " + page.toString() + ")");
            });
    }

    protected setMenu() {
        // if (this.$menu) { WANT it to update menu after each load for React EventBusDisplayContainer
        //     return;
        // }
        this.$menu = this.getMenu();

        if (this.$menu != null) {
            MenuUpdateEvent.bus.fire(new MenuUpdateEvent("nav", this.$menu));
        }
    }

    protected abstract getMenu(): $.JQuery;
}

export = PagedAsyncTableControl;
