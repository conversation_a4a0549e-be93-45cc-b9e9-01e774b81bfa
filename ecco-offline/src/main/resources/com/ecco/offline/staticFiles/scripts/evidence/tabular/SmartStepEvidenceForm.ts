
import BaseEvidenceForm = require("../BaseEvidenceForm");
import GoalsControl = require("./GoalsControl");
import { ServiceRecipientWithEntities, FlagEvidenceDto } from 'ecco-dto';
import EvidenceCommentForm = require("../EvidenceCommentForm");
import {MultiEntryEvidenceForm} from "../MultiEntryEvidenceForm";
import WizardState = require("../../controls/WizardState");

import EvidenceEmbeddedSwitcherControl = require("../EvidenceEmbeddedSwitcherControl");
import OutcomesControl = require("../../evidence/tabular/OutcomesControl");
import WizardView = require("../../controls/WizardView");
import {SupportWork} from "ecco-dto/evidence-dto";
import {EvidenceDef} from "ecco-evidence";
import {EvidenceGroup} from "ecco-dto";
import {ActivityType} from "ecco-dto/service-config-dto";

import {CommandQueue} from "ecco-commands";
import {removeDraftsFromPage} from "../../controls/autosave";
import {WorkUuidResolver} from "ecco-commands";


export class SmartStepEvidenceForm extends BaseEvidenceForm implements WizardView {

    private outcomesControl: OutcomesControl;
    private commentForm: EvidenceCommentForm;
    private multiForm: MultiEntryEvidenceForm;
    private advanceWizardPage: () => void;

    constructor(workUuidQResolver: WorkUuidResolver,
                planAllocatedId: string,
                embeddedSwitcher: EvidenceEmbeddedSwitcherControl,
                referral: ServiceRecipientWithEntities,
                activityTypeInterest: ActivityType[],
                risksOutstanding: SupportWork[],
                evidenceDef: EvidenceDef,
                _title: string,
                private onCompleted: () => void,
                commandQueue: CommandQueue,
                previousCommandQueue: CommandQueue,
                eventId?: string) {
        super(workUuidQResolver, commandQueue, planAllocatedId, embeddedSwitcher, referral, activityTypeInterest, evidenceDef,
                {}, _title, undefined, undefined, previousCommandQueue, eventId);

        let serviceType = this.serviceRecipient.configResolver.getServiceType();
        // NB we can't pass the data in the constructor and expect it to be available in methods below because 'super'
        // above is called before private vars are set, and so 'super' calls createCommentControl but risksOutstanding is
        // undefined. Therefore we wait for the super call to complete, and populate the data now.
        if (serviceType
            .taskDefinitionSettingHasFlag(evidenceDef.getTaskName(),  "showCommentComponents", "showRiskManagementDealsWith")) {
            this.commentForm.populateRiskOutstanding(risksOutstanding);
        }

    }

    public createCommentControl(): EvidenceCommentForm {

        if (this.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat) {
            this.commentForm = new EvidenceCommentForm(this.serviceRecipient,
                this.evidenceDef,
                () => this.submitForm(), this.workUuidResolver
                );
        }
        else {
            this.commentForm = new EvidenceCommentForm(this.serviceRecipient,
                this.evidenceDef,
                () => this.submitForm(), this.workUuidResolver,
                undefined, this.evidenceDef.getReviewId(),
                this.previousCommandQueue,
                this.eventId);
        }
        return this.commentForm;
    }

    protected createCommentControlMore(): MultiEntryEvidenceForm {
        const canMultiEntry = this.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(this.evidenceDef.getTaskName(),  "useMultiEntry");
        const isEligible = this.serviceRecipient.features.hasRoleTrial1();
        if (canMultiEntry && isEligible) {
            this.multiForm = new MultiEntryEvidenceForm(this.workUuidQResolver, this.serviceRecipient, this.evidenceDef);
        }
        return this.multiForm;
    }

    public createMainControl() {

        if (this.serviceRecipient.configResolver.getServiceType()
                .getTaskDefinitionSetting(this.evidenceDef.getTaskName(),  "showEvidenceStyleAs") == "goals") {
            this.outcomesControl = new GoalsControl(this.planAllocatedId, this.serviceRecipient, this.serviceRecipientActivityTypeInterest,
                this.workUuidResolver, this.evidenceDef,
                (flagEvidenceByFlagId: {[id: number]: FlagEvidenceDto}) => this.commentForm.populateFlags(flagEvidenceByFlagId));
            return this.outcomesControl;
        }

        this.outcomesControl = new OutcomesControl(this.planAllocatedId, this.serviceRecipient, this.serviceRecipientActivityTypeInterest,
            this.workUuidResolver, this.evidenceDef,
            (flagEvidenceByFlagId: {[id: number]: FlagEvidenceDto}) => this.commentForm.populateFlags(flagEvidenceByFlagId));
        return this.outcomesControl;

    }

    public setOnLoad(exe: () => void) {
        this.outcomesControl.setOnLoad(exe);
    }
    public setWizardTriggerPage(exe: () => void) {
        this.advanceWizardPage = exe;
    }
    public setWizardPreviousPage(exe: () => void) {
    }
    public setWizardCancelPage(exe: () => void) {
    }
    public setWizardRerenderPage(exe: () => void) {
    }
    public renderWithCancelConfirmation() {
    }
    public getWizardInitialPage() {
        return this.evidenceDef.getReviewPage();
    }
    public renderWithPage(currentPage: number) {
        this.outcomesControl.renderWithPage(currentPage);
        this.resetWorkUuid();
        return super.element();
    }

    public setGetWizardState(getState: () => WizardState) {
    }

    public wizardCompleted() {
        this.onCompleted();
    }

    /** True if required fields are set */
    public isValid(): boolean {
        return this.outcomesControl ? this.outcomesControl.isValid() : true;
    }

    protected getChangesAsCommands() {
        const cmdQueue = super.getChangesAsCommands();
        if (this.multiForm) {
            this.multiForm.emitTo(cmdQueue, this.commentControl.getStartDate())
        }
        return cmdQueue;
    }
    protected populateReturningCommandQueue() {
        super.populateReturningCommandQueue();
        if (this.multiForm) {
            this.multiForm.emitTo(this.commandQueue, this.commentForm.getStartDate())
        }
    }

    protected afterSubmitted() {
        if (this.advanceWizardPage) {
            this.advanceWizardPage();
            this.commentForm.cleanCommentsAndTime();
        } else {
            // debounce can still save after this
            removeDraftsFromPage(); // TODO: Simplify all this crazyness
            this.onCompleted();
        }
    }

}

export default SmartStepEvidenceForm;
