import {applicationRootPath} from "application-properties";
import * as React from "react";
import {FC} from "react";
import {
    CareVisitH<PERSON>ory<PERSON>ontroller,
    LoadingSpinner,
    ServiceAgreementsView,
    SimpleSchemaFormRenderer,
    TabsBuilder,
    LoadSRWithEntitiesContext,
    useServiceRecipientWithEntities,
    useServicesContext
} from "ecco-components";
import {Box} from '@eccosolutions/ecco-mui';
import {ServiceRecipientContactHistory} from "../../contacts/components/ContactHistory";
import {CommunicationActionButton} from "../../contacts/components/communicationControls";
import {lazyControlWrapper} from "../../components/ControlWrapper";
import {CustomForm, getCustomFormWrapper} from "../../referral/components/CustomForm";
import {CommandFormButton} from "../../components/CommandForm";

const ServiceRecipientTasks = React.lazy(() => import("../../service-recipients/ServiceRecipientTasks"));
const AuditHistory = React.lazy(() => import("../../service-recipients/components/AuditHistory").then(i => ({default: i.AuditHistory})));
const TasksControl = React.lazy(() => import("../../workflow/tasklist/TasksControl"));

interface Props {
    srId: number
    srName: string
    isCareRun: boolean
}

/**
 * Appears when clicking on the title of an appointment (in the rota information panel at the top).
 */
const ServiceRecipientView: FC<Props> = ({srId, srName, isCareRun}) => {

    const {sessionData} = useServicesContext();
    const {context, loading} = useServiceRecipientWithEntities(srId)

    const SupportHistory = lazyControlWrapper(
        () => import("../../evidence/support/SupportHistoryListControl"),
        // @ts-ignore - because typescript
        srId, "needsReduction"
    );

    if (loading) {
        return <LoadingSpinner/>
    }

    const {serviceType} = context
    const supportTaskName = serviceType.getFirstSupportTaskName(sessionData)
    const supportTitle = sessionData.fileSupportLabel(serviceType);
    const supportHistoryTitle = sessionData.fileSupportHistoryLabel(serviceType);

    const formDefinition = context.serviceType.getTaskDefinitionSetting("customForm1", "formDefinition")
    const showShiftGuidanceTab = isCareRun && !!formDefinition

    const shiftGuidanceContent = showShiftGuidanceTab && <>
        <span className="pull-right">
            <CommandFormButton buttonLabel="edit guidance" modalTitle="edit shift guidance" color="primary">
                <CustomForm
                        serviceRecipientId={srId}
                        taskName="customForm1"
                        taskNameGroup="customForm1"
                        taskHandle={null} />
            </CommandFormButton>
        </span>
        {getCustomFormWrapper(
                srId, "customForm1", "customForm1",
                null, null, 'embedded', true, SimpleSchemaFormRenderer)}
    </>;

    const Tabs = ({srId, context}) => new TabsBuilder()
        .addTab("overview",
            <span>{isCareRun ? "run: " : "client: "}
                <a href={`${applicationRootPath}nav/service-recipient/${srId}`} target="_blank">
                    {srName}
                </a>
            </span>,
            undefined, "fa-user")
        .addTab("shift guidance", shiftGuidanceContent, showShiftGuidanceTab, "fa-warning")
        .addTab("agreements",
            <ServiceAgreementsView
                serviceRecipientId={srId}
            />,
            undefined, "fa-calendar")
        .addTab(
            "communication",
                () => <>
                <div className="text-right">
                    <CommunicationActionButton contactId={context.client.contactId}/>
                </div>
                <div style={{margin: "8px -8px"}}>
                    <ServiceRecipientContactHistory srId={srId}/>
                </div>
            </>,
            !isCareRun && sessionData.hasRoleSysAdmin() && sessionData.isEnabled('referralOverview.communication') && context.client != null, "fa-phone")
        .addTab(
            supportTitle,
            /*lazy loaded avoids a NPE on supportTaskName*/
            () => <ServiceRecipientTasks srId={srId} taskName={supportTaskName}/>,
            sessionData.isEnabled("referralOverview.support") && supportTaskName != null,
            "fa-edit"
        )
        .addTab(
            supportHistoryTitle,
            <SupportHistory/>,
            undefined, "fa-history")
        .addTab(
                "visit history",
                () => <CareVisitHistoryController srId={srId}/>,
                undefined, "fa-history")
        .addTab(
            "audit history",
            <AuditHistory serviceRecipientId={srId} sessionData={sessionData}/>,
            undefined, "fa-history")
        .addTab("tasks (sys admin only)",

            <LoadSRWithEntitiesContext srId={srId}>
                    <TasksControl srId={srId}/>
            </LoadSRWithEntitiesContext>,
            sessionData.hasRoleSysAdmin(), "fa-check-square-o")
        .build();

    return <Box p={2}>
        <Tabs srId={srId} context={context}/>
    </Box>
}

export default ServiceRecipientView;