import EccoElement = require("../controls/Element");
import {ActionComponent} from "ecco-dto";
import {CommandSource} from "ecco-commands";
import {BaseServiceRecipientCommandDto, GoalUpdateCommandDto} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    EvidenceContext,
    EvidenceDef,
    EvidenceDisplayOptions,
    ActionInstanceFeatures,
    ActionInstanceControlData,
    HierarchyPosition
} from "ecco-evidence";
import {SessionData} from "ecco-dto";
import {CommandQueue} from "ecco-commands";

export interface EvidenceControl extends EccoElement {
    emitChangesTo(queue: CommandQueue);
}

/** Behaviour for a collection of ActionGroup's (ie tab-level)
 * This only applies to tabular evidence */
export interface ActionGroupDefs extends CommandSource {
    render();
    isValid(): boolean;
    isEmpty(): boolean;
    isRelevant(): boolean;
}

/**
 * Behaviour for an ActionGroup (ie many actionDefId) that can hold
 * many ActionDefParent's.
 */
export interface ActionGroupDef extends CommandSource, EccoElement {
    render();
    isValid(): boolean;
    count(): number;
    relevantCount(): number;
    hasActionDefId(actionDefId: number);
    /** Dynamically */
    ensureControlInPlace(ctl: ActionDefParent, position: HierarchyPosition);
    changedDisplayOptions(displayOptions: EvidenceDisplayOptions);
}

/**
 * Behaviour for parent-data. This is the notion that evidence can be grouped
 * within a parent, where there can be many parents per ActionGroup.
 */
export interface ActionDefParent extends CommandSource, EccoElement {
    render();
    getControlUuid(): string; // unique to each leaf
    getRootUuid(): string; // unique to a branch
    getActionDefId(): number; // not unique across the branches
    getHierarchy(): number;
    getPosition(): string;
    isValid(): boolean;
    isRelevant(): boolean;
    count(): number;
    isAppliedToDom(): boolean; // not nice, but helpful check when updating through commands
    setAppliedToDom(boolean);
    changedDisplayOptions(displayOptions: EvidenceDisplayOptions);
}

export interface ActionInstanceControlFactory<CHILD_TYPE> {
    new(context: EvidenceContext, sessionData: SessionData, serviceRecipientId: number,
        evidenceDef: EvidenceDef, actionDef: ActionComponent,
        initialData: ActionInstanceControlData, controlUuid: Uuid,
        features: ActionInstanceFeatures): CHILD_TYPE;
}

/** Behaviour for child-data - specific instances */
export interface ActionInstanceControl extends CommandSource, EccoElement {
    initialActionInstanceUuid(): string;
    actionInstanceUuidOrControlUuid(): string;
    getActionDefId(): number;
    getActionDef(): ActionComponent;
    getHierarchyPosition(): HierarchyPosition;
    init();
    render();
    isRelevant(): boolean;
    isAchieved(): boolean;
    isValid(): boolean;
    showSummaryOnly();
    hasHistory(): boolean;
    applySnapshot(sa: ActionInstanceControlData);
    applyCommand(cmd: BaseServiceRecipientCommandDto);
    applyCommand(dto: GoalUpdateCommandDto);
    changedDisplayEvidence(displayOptions: EvidenceDisplayOptions): boolean;
}
