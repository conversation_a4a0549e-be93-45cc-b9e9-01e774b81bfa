import * as React from "react";
import {createInstance, FulfilledProps} from "react-async";
import * as services from "ecco-offline-data";
import {stringifyPossibleError, withAuthError<PERSON>andler} from "ecco-offline-data";
import {LoadingSpinner} from "ecco-components";
import {ReferralDto} from "ecco-dto";
import {ReportAjaxRepository} from "../reports/ReportAjaxRepository"
import {EccoDate} from "@eccosolutions/ecco-common";
import Lazy = require("lazy");

export interface ReferralsWithEventsContext {
    data: ReferralDto[];
}

// load all referrals live with their events for a week (no filtering on event category)
// shows the EventCard as per 'nearby'
// it does not seem to use the chartDefUuid at all, which comes from calendarlist/router but doesn't appear to be reachable
function loadReportData(props: {chartDefUuid: string}): Promise<ReferralsWithEventsContext> {
    const start = EccoDate.todayUtc();
    const end = EccoDate.todayUtc().addDays(7);
    const dataQ: Promise<ReferralDto[]> = services.getReferralRepository()
        .findAllReferralSummary(ReportAjaxRepository.generateLiveReportCriteria())
        .then((referrals: ReferralDto[]) =>
            Lazy(referrals)
                .filter(r => r.contactId != null)
                .map(r =>
                    services.getCalendarRepository().fetchCalendarsByContactIds([r.contactId], start, end)
                        .then(events => {
                            r.calendarEvents = events;
                            return r;
                        })
                )
                .flatten<Promise<ReferralDto[]>>()
                .reduce((chainedQ:Promise<ReferralDto[]>, currQ:Promise<ReferralDto[]>): Promise<ReferralDto[]> =>
                    Promise.all<ReferralDto[]>([chainedQ, currQ])
                        .then( ([chainedReferrals, currReferrals]) =>
                            Promise.resolve(chainedReferrals.concat(currReferrals))), Promise.resolve([]))
        );
    return withAuthErrorHandler(dataQ.then(data => ({data})));
}

const AsyncReferralsWithEventsContext = createInstance<ReferralsWithEventsContext>(
    {promiseFn: loadReportData}
);



/** Use this when you are in control of the data you are loading.
 * If you just want to access the data, then use Async....Resolved
 * initialValue will come in useful for testing that things render correctly and when we want
 * to test audit first behaviour based on initial load
 */
export function ReferralsWithEventsLoadAndRender(
    props: {chartDefUuid?: string, initialValue?: ReferralsWithEventsContext} & FulfilledProps<ReferralsWithEventsContext>) {

    return <AsyncReferralsWithEventsContext {...props} watch={props.chartDefUuid}>
        <AsyncReferralsWithEventsContext.Loading>
            <LoadingSpinner/>
        </AsyncReferralsWithEventsContext.Loading>
        <AsyncReferralsWithEventsContext.Resolved>
            {/*{
                (data: ReferralsWithEventsContext, state: AsyncState<ReferralsWithEventsContext>) => {
                    ReloadEvent.bus.addHandler(state.reload);
                    return typeof props.children === "function" ? props.children(data, state) : props.children;
                }
            }*/}
            {props.children}
        </AsyncReferralsWithEventsContext.Resolved>
        <AsyncReferralsWithEventsContext.Rejected>
            {error => {
                console.error(error);
                return stringifyPossibleError(error);
            }}
        </AsyncReferralsWithEventsContext.Rejected>
    </AsyncReferralsWithEventsContext>;
}