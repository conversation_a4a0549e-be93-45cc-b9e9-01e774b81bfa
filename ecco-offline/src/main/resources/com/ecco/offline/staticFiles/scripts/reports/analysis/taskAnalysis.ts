import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {
    Analyser,
    extractPair,
    Group,
    GroupedAnalysis,
    SequenceAnalysis,
    SingleRecordAnalysis,
    Transformer
} from "./types";
import {TaskStatus} from "ecco-dto/evidence-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {AnalysisContext} from "../charts/domain";
import {
    columnMap,
    dateColumn,
    dateTimeColumn,
    dateTimeColumnFromIsoUtc,
    hrefColumn,
    HrefData, joinColumnMaps, joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../../controls/tableSupport";
import {
    EntityType,
    entityUrl, referralSummaryColumns
} from "../tables/predefined-table-representations";
import {ReferralDto, ReferralSummaryDto} from "ecco-dto/referral-dto";
import {mountWithServices} from "../../offline/ServicesContextProvider";
import {showInModalEditTask} from "ecco-components";
import {SessionData} from "ecco-dto";
import {applicationRootPath} from "application-properties";


//*********************************
// Analysis: Task

export class TaskAnalysis extends SequenceAnalysis<TaskStatus> {
    constructor(ctx: AnalysisContext, data:Sequence<TaskStatus>) {
        super(ctx, data, (item: TaskStatus) => item.taskInstanceUuid.toString());
        this.derivativeAnalysers = {
            "filterByNotCompleted": taskStatusFilterByNotCompletedAnalyser,
            "groupByDue": taskStatusGroupByDueAnalyser,
            "groupByService": taskStatusGroupByServiceAnalyser,
            "groupByProject": taskStatusGroupByProjectAnalyser,
            "groupByAssignee": taskStatusGroupByAssigneeAnalyser,
            "tasksToSingleSummary": tasksToSingleSummaryAnalyser
        };
        this.recordRepresentation = {
            "TaskStatusOnlyColumns": taskStatusOnlyColumns,
            "TaskStatusWithReferralColumns": taskStatusWithReferralColumns,
            "TaskStatusWithAuditColumns": taskStatusWithAuditColumns
        }
    }
}

const referralHref: (referral: ReferralSummaryDto) => HrefData = (referral) => {return {
    display: referral.referralCode || referral.referralId.toString(),
    url: entityUrl(referral.referralId.toString(), EntityType.Referral)
}};

const taskParentHref: (referral: ReferralDto | ReferralSummaryDto) => HrefData = (referral) => {return {
    display: referral.parentId?.toString() || referral.serviceRecipientId.toString(),
    url: `${applicationRootPath}nav/r/main/sr2/${referral.serviceRecipientId}/`
}};

const taskType: (referral: ReferralDto | ReferralSummaryDto) => string = (referral) =>
    referral.prefix == "r" ? "referral" : referral.prefix == "i" ? "incident" : referral.prefix == "m" ? "repair" : referral.prefix == "b" ? "building" : referral.prefix;

export class TaskStatusDisplay {
    static getHref(task: TaskStatus) {return taskParentHref(task.referralSummary)}
    static getReferralHref(task: TaskStatus) {return referralHref(task.referralSummary)}
    static getReferralCode(task: TaskStatus) {return task.referralSummary.referralCode || task.referralSummary.referralId.toString()}
    static getClientCode(task: TaskStatus) {return task.referralSummary.clientCode || task.referralSummary.clientId.toString()}
    static getTaskType(task: TaskStatus) {return taskType(task.referralSummary)}
    static getClientName(task: TaskStatus) {return task.referralSummary.clientDisplayName}
    static getName(task: TaskStatus) {return task.referralSummary.displayName}
    static getServiceDescription(task: TaskStatus, sessionData: SessionData) {return sessionData.getServiceCategorisationName(task.referralSummary.serviceAllocationId)}
    static getServiceName(task: TaskStatus, sessionData: SessionData) {return task.referralSummary.serviceAllocationId ? sessionData.getServiceName(sessionData.getServiceCategorisation(task.referralSummary.serviceAllocationId).serviceId) : "no service"}
    static getProjectName(task: TaskStatus, sessionData: SessionData) {return sessionData.getProjectName(sessionData.getServiceCategorisation(task.referralSummary.serviceAllocationId).projectId)}
    static getTaskName(task: TaskStatus, sessionData: SessionData) {return sessionData.lookupTaskName(task.taskDefinitionId, sessionData.getServiceTypeByServiceCategorisationId(task.serviceAllocationId).id, task.taskDefName)}
    static getCreated(task: TaskStatus) {return EccoDateTime.parseIso8601Utc(task.created)}
    static getDueDateTime(task: TaskStatus) {return task.dueDate ? EccoDateTime.parseIso8601(task.dueDate).toEccoDate() : null}
    static getCompleted(task: TaskStatus) {return EccoDateTime.parseIso8601Utc(task.completed)}
    static editHandler(task: TaskStatus) {
        return () => mountWithServices(
            // LinearWorkflowService toHandle shows taskHandle as serviceRecipientId-taskDefId (taskDefId is the referralAspectId)
            // but we've added a taskInstanceUuid so that we get the actual, historical, event
            showInModalEditTask(`${task.serviceRecipientId}-${task.taskDefinitionId}-${task.taskInstanceUuid}`, task.serviceAllocationId), document.createElement("div")
        )
    }
}

const taskStatusToReferralColumns = joinNestedPathColumnMaps<TaskStatus, ReferralSummaryDto>("r",
        (row) => row.referralSummary, referralSummaryColumns);

let taskStatusOnlyColumns =  columnMap(
    textColumn<TaskStatus>("taskId", (row) => row.taskInstanceUuid),
    numberColumn<TaskStatus>("srId", (row) => row.serviceRecipientId),

    // TODO better to use the join style rather than specify r-id again here
    hrefColumn<TaskStatus>("id", (row) => TaskStatusDisplay.getHref(row)),
    hrefColumn<TaskStatus>("r-id", (row) => TaskStatusDisplay.getReferralHref(row)),
    textColumn<TaskStatus>("c-id", (row) => TaskStatusDisplay.getClientCode(row)),
    textColumn<TaskStatus>("type", (row) => TaskStatusDisplay.getTaskType(row)),
    textColumn<TaskStatus>("client", (row) => TaskStatusDisplay.getClientName(row)),
    textColumn<TaskStatus>("name", (row) => TaskStatusDisplay.getName(row)),
    textColumn<TaskStatus>("service", (row, ctx) => TaskStatusDisplay.getServiceName(row, ctx.getSessionData())),
    textColumn<TaskStatus>("project", (row, ctx) => TaskStatusDisplay.getProjectName(row, ctx.getSessionData())),
    textColumn<TaskStatus>("location", (row, ctx) => TaskStatusDisplay.getProjectName(row, ctx.getSessionData())),
    textColumn<TaskStatus>("task", (row, ctx) => TaskStatusDisplay.getTaskName(row, ctx.getSessionData())),
    textColumn<TaskStatus>("description", (row) => row.description),
    dateTimeColumn<TaskStatus>("created", (row) => TaskStatusDisplay.getCreated(row)),
    dateColumn<TaskStatus>("due", (row) => TaskStatusDisplay.getDueDateTime(row)),
    hrefColumn<TaskStatus>("edit", row => {
        return {
            display: "edit",
            click: TaskStatusDisplay.editHandler(row)
        }
    }),
    dateTimeColumn<TaskStatus>("completed", (row) => TaskStatusDisplay.getCompleted(row)),
    numberColumn<TaskStatus>("assigneeId", (row) => row.assignee),
    textColumn<TaskStatus>("assignee", (row) => row.assigneeDisplayName)
);
const taskStatusWithReferralColumns = joinColumnMaps(taskStatusOnlyColumns, taskStatusToReferralColumns);

let taskStatusAuditOnlyColumns =  columnMap(
    dateTimeColumnFromIsoUtc<TaskStatus>("audit", (row) => row.lastAudit?.timestamp)
)
const taskStatusWithAuditColumns = joinColumnMaps(taskStatusOnlyColumns, taskStatusAuditOnlyColumns);


// **********
// FILTER
// **********
export const taskStatusFilterByNotCompletedAnalyser: Transformer<TaskStatus, TaskStatus>
    = function(ctx: AnalysisContext, input: Sequence<TaskStatus>): SequenceAnalysis<TaskStatus> {
    let notComplete = input.filter(t => t.completed == null);
    return new TaskAnalysis(ctx, notComplete);
};


// **********
// GROUP
// **********
// interface GroupByKeyFn<T> {
//     (input: Sequence<T>, extractKey: (input: Sequence<T>) => string): Sequence<Group<T>>;
// }
function countsBy<T>(input: Sequence<T>, extractKey: (input: T) => string): Sequence<Group<T>> {
    return input.groupBy((inputElement) => extractKey(inputElement))
        .pairs()
        .map( extractPair )
        .map((pair) => {
            return {
                    key: pair.key,
                    count: pair.elements.size(),
                    elements: pair.elements
                };
        });
}
// function groupBy(input: Sequence<TaskStatus>, extractKey: (input: TaskStatus) => string): Sequence<Group<TaskStatus>> {
//     return input.groupBy((inputElement) => extractKey(inputElement))
//             .pairs()
//             .map( extractPair );
// }

let taskStatusGroupByServiceAnalyser: Transformer<TaskStatus, Group<TaskStatus>>
    = function(ctx: AnalysisContext, input: Sequence<TaskStatus>): SequenceAnalysis<Group<TaskStatus>> {
    return new GroupedTaskStatusAnalysis(ctx, countsBy(input, (task) => TaskStatusDisplay.getServiceName(task, ctx.getSessionData())));
};

let taskStatusGroupByProjectAnalyser: Transformer<TaskStatus, Group<TaskStatus>>
    = function(ctx: AnalysisContext, input: Sequence<TaskStatus>): SequenceAnalysis<Group<TaskStatus>> {

    return new GroupedTaskStatusAnalysis(ctx, countsBy(input, t => ctx.getSessionData().getProjectName(ctx.getSessionData().getServiceCategorisation(t.referralSummary.serviceAllocationId).projectId) || "no project"));
};

let extractAssigneeName = (input: TaskStatus) => input.assigneeDisplayName || "no assignee";
let taskStatusGroupByAssigneeAnalyser: Transformer<TaskStatus, Group<TaskStatus>>
    = function(ctx: AnalysisContext, input: Sequence<TaskStatus>): SequenceAnalysis<Group<TaskStatus>> {
    return new GroupedTaskStatusAnalysis(ctx, countsBy(input, extractAssigneeName));
};

let extractDue = (input: TaskStatus) => {
    let dte = input.dueDate ? EccoDateTime.parseIso8601(input.dueDate).toEccoDate() : null;
    return getQuantisedDue(dte);
};
function getQuantisedDue(value: EccoDate): string {
    if (!value) {
        return "not due";
    }
    if (EccoDate.todayLocalTime().laterThan(value)) { return "overdue"; }
    if (EccoDate.todayLocalTime().addDays(7).laterThan(value)) { return "due soon (7 days)"; }
    return "later";
}
let taskStatusGroupByDueAnalyser: Transformer<TaskStatus, Group<TaskStatus>>
    = function(ctx: AnalysisContext, input: Sequence<TaskStatus>): SequenceAnalysis<Group<TaskStatus>> {
    return new GroupedTaskStatusAnalysis(ctx, countsBy(input, extractDue));
};
class GroupedTaskStatusAnalysis extends GroupedAnalysis<TaskStatus, Group<TaskStatus>> {
    constructor(ctx: AnalysisContext, data: Sequence<Group<TaskStatus>>) {
        super(ctx, data);
        //this.recordRepresentation = {
        //    "GroupTaskStatusColumns": groupTaskStatusColumns
        //};
        // this.derivativeAnalysers = {
        //     "unGroupRelationship": unGroupWorkWithRefGroupWithCountAnalyser // WIP
        // };
        this.addOnClickAnalyser("ungroup", wrapTaskStatusAnalyser);
        this.addOnClickManyAnalysis("ungroup", TaskAnalysis);
    }
}
// let groupTaskStatusColumns = columnMap(
//     // choose one of these 'key' columns according to the analyser before this chart/table
//     textColumn<Group<TaskStatus>>("key", (row) => row.key),
//     numberColumn<Group<TaskStatus>>("count", (row) => row.count)
// );
let wrapTaskStatusAnalyser: Analyser<Group<TaskStatus>, Sequence<TaskStatus>>
    = function(ctx: AnalysisContext, input: Group<TaskStatus>): TaskAnalysis {
    return new TaskAnalysis(ctx, input.elements);
};


// **********
// SUMMARY BADGES TO BREAKDOWN
// **********
interface CountGroupSummary {
    count1: number;
    count2?: number;
    count3?: number;
}
const byTaskGroupColumns = columnMap(
        numberColumn<CountGroupSummary>("due", row => row.count1),
        numberColumn<CountGroupSummary>("overdue", row => row.count2),
        numberColumn<CountGroupSummary>("completed", row => row.count3),
);
class CountGroupToSingleSummaryAnalysis extends SingleRecordAnalysis<CountGroupSummary> {
    constructor(ctx: AnalysisContext, data: CountGroupSummary, input: Sequence<TaskStatus>) {
        super(ctx, data);
        this.recordRepresentation = {
            "byTaskGroupAnalysis": byTaskGroupColumns
        }
        // NB the clickAnalyser's here use the parent/input data, which means we lose the grouping,
        // therefore, we use the analyser name to provide the grouping
        const dueFilter = input.filter(t => !t.completed && EccoDateTime.parseIso8601(t.dueDate).toEccoDate().laterThanOrEqual(EccoDate.todayUtc()));
        this.addOnClickAnalyser("selectAllDue", () => new TaskAnalysis(ctx, dueFilter));
        const overdueFilter = input.filter(t => !t.completed && EccoDateTime.parseIso8601(t.dueDate).toEccoDate().earlierThan(EccoDate.todayUtc()));
        this.addOnClickAnalyser("selectAllOverdue", () => new TaskAnalysis(ctx, overdueFilter));
        const completedFilter = input.filter(t => !!t.completed);
        this.addOnClickAnalyser("selectAllCompleted", () => new TaskAnalysis(ctx, completedFilter));
    }
}
const tasksToSingleSummaryAnalyser: Analyser<Sequence<TaskStatus>, CountGroupSummary>
        = function (ctx: AnalysisContext, input: Sequence<TaskStatus>): CountGroupToSingleSummaryAnalysis {
    const summary: CountGroupSummary = {
        // due is all not completed and due today or after
        count1: input.filter(t => !t.completed && EccoDateTime.parseIso8601(t.dueDate).toEccoDate().laterThanOrEqual(EccoDate.todayUtc())).toArray().length,
        // overdue is not completed and due before today
        count2: input.filter(t => !t.completed && EccoDateTime.parseIso8601(t.dueDate).toEccoDate().earlierThan(EccoDate.todayUtc())).toArray().length,
        // completed
        count3: input.filter(t => !!t.completed).toArray().length
    }
    return new CountGroupToSingleSummaryAnalysis(ctx, summary, input);
};
