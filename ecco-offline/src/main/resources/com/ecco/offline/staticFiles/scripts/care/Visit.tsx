import * as React from "react";
import {FC} from "react";
import {
    CareVisitProps,
    CareVisitRoot,
    CareVisitSummaryCard,
    LoadingSpinner, useCareOrEventCard,
    useCareVisitMenuContext
} from "ecco-components";
import {EventCard} from "../components/EventCard";
import {EventResourceDto} from "ecco-dto";


interface VisitProps {
    showDate?: boolean;
    event: EventResourceDto;
    showEventAsVisit: boolean
}

// see CareVisitHistory
export const VisitController: FC<{eventId: string}> = ({eventId}) => {
    return <div>visit controller</div>
    //return <Visit event={null} showDate={null}/>
}

export const searchAnywhereMatchesCard = (searchText: string, data: CareVisitProps) => {
    if (!searchText) {
        return true;
    }
    // TODO we should make things readable, dates and remove id's
    const matchObj = {
        displayName: data.displayName,
        address: data.address,
        visitType: data.visitType,
        aptTypeId: data.aptTypeId,
        visitDateTime: data.visitDateTime, // string, but possibly could format as '11-May'
        plannedDateTime: data.plannedDateTime,
        plannedMins: data.plannedMins,
        serviceRecipientId: data.serviceRecipientId,
        supportWorker: data.supportWorker,
        scheduleInfo: data.scheduleInfo,
        taskSummary: data.taskSummary,
        additionalStaff: data.additionalStaff?.additionalStaffNames.join(", ")
        //commentForm:
    };
    return Object.keys(matchObj).some(key => matchObj[key] &&
            matchObj[key].toString().toLowerCase().includes(searchText.toLowerCase()));
}

export const Visit: FC<VisitProps> = props => {

    const ctxMenu = useCareVisitMenuContext();
    const {loaded, data: visitProps} = useCareOrEventCard(props.event, props.showEventAsVisit);

    // if no data returned, then it's because it's an event and now showEventAsVisit
    if (loaded && !visitProps) {
        return <EventCard event={props.event}/>;
    }
    if (!loaded) {
        return <LoadingSpinner/>;
    }

    const display = searchAnywhereMatchesCard(ctxMenu.searchData.searchText, visitProps);

    return (display && <CareVisitRoot careVisitInitState={visitProps}>
            <CareVisitSummaryCard />
        </CareVisitRoot>
    );
}
