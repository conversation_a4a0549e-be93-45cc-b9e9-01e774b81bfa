import bloodhound = require("bloodhound");
import $ = require("jquery");
import events = require("../common/events");

import Accordion = require("../controls/Accordion");
import SearchableListControl = require("../controls/SearchableListControl");
import SearchableListControlOptions = require("../controls/SearchableListControlOptions");
import SummarisedElement = require("../controls/SummarisedElement");
import WorkerSummaryControl = require("./WorkerSummaryControl");
import Bloodhound = bloodhound.Bloodhound;
import {apiClient} from "ecco-components";
import {WorkersAjaxRepository} from "ecco-dto";
import {StaffDto} from "ecco-dto/hr-dto";
import {withAuthErrorHandler} from "ecco-offline-data";
import services = require("ecco-offline-data");

var repository = new WorkersAjaxRepository(apiClient);

class WorkersListControlOptions implements SearchableListControlOptions<StaffDto> {

    constructor(private buildingId: number = null){
    }

    public placeholderText = "e.g. w-id or staff name";

    /** Create a control for the given item */
    public createControl(item: StaffDto): SummarisedElement<StaffDto> {
        return new WorkerSummaryControl(item);
    }

    public loadInner(callback: (items: StaffDto[]) => void): void {
        var workers = this.buildingId
            ? repository.findAllWorkersAtBuilding(this.buildingId)
            : repository.findAllWorkers();
        withAuthErrorHandler(workers).then( items => {
            callback(items)
        });
    }

    public generateKey(item: StaffDto) {
        return String(item.workerId);
    }

    public generateSearchConfig(items: StaffDto[]) {
        var searchConfig: bloodhound.BloodhoundConfig<StaffDto> = {
            datumTokenizer: (worker: StaffDto) => {
                var result = new Array<string>();
                result.push(worker.workerId.toString());
                if (worker.firstName) {
                    result.push(worker.firstName);
                }
                if (worker.lastName) {
                    result.push(worker.lastName);
                }
                return result;
            },
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: items,
            limit: 10
        };
        return searchConfig;
    }
}

/** Searchable Workers list, built on top of SearchableListControl */
export class WorkersListControl extends SearchableListControl<StaffDto> {

    public static fromBuildingSrId(srId: number): Promise<WorkersListControl> {
        return services.getBuildingRepository().findOneBuildingBySrId(srId).then(bldg => {
            return new WorkersListControl(bldg.buildingId)
        });
    }

    constructor(private buildingId: number = null) {
        super(Accordion, new WorkersListControlOptions(buildingId));

        var $menu = $("<div>").css({"text-align":"center"}).text("workers");
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $menu) );

    }
}
export default WorkersListControl;