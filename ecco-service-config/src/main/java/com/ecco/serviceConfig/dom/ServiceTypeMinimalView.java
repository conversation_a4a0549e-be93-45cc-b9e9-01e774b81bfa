package com.ecco.serviceConfig.dom;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * Acts as a class that we can migrate to which allows us to work with a ServiceType
 * as an Entity without loading the world on the existing ServiceType. There should
 * be no mapping files here ideally, but at least they are lazy.
 */

@Entity
@Table(name = "servicetypes")
@Getter
@Setter
public class ServiceTypeMinimalView extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    private String name;

    private boolean hideOnList;

    private boolean hideOnNew;

    private Long primaryRelationshipId;

    public ServiceTypeMinimalView() {
        // needed for javassist
    }

}
