package com.ecco.serviceConfig.dom;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ecco.serviceConfig.dom.ServiceType_TaskDefinition.isWizardTask;
import static com.google.common.collect.Maps.uniqueIndex;

@Slf4j
@Entity
@Getter
@Setter
@Table(name = "servicetypes")
public class ServiceType extends AbstractLongKeyedEntity {

    private static final long serialVersionUID = 1L;

    public static final String BUILDINGS = "buildings";
    public static final String HR = "hr";
    public static final String SUPPORT = "support";
    public static final String CONTRACT = "contracts";
    public static final String GROUPSUPPORT = "groupsupport";
    public static final String REPAIR = "repair";
    public static final String MANAGED_VOID = "mvoid";

    private String name;

    private String type = SUPPORT; // default in the database too.

    private boolean hideOnList;
    private boolean hideOnNew;

    /**
     * If this is non null, this service type is an associated/secondary/non-primary service type.
     * Currently used by family support to allow each 'party' (referral) to have their own service type.
     */
    private Long primaryRelationshipId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "multiId.serviceType", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("orderby ASC")
    private Set<ServiceType_TaskDefinition> taskDefinitions;

    @OneToOne
    @PrimaryKeyJoinColumn
    private ServiceTypeWorkflow workflow;

    // helpful associations for a service type to 'own' these associations
    // the individual classes (eg OutcomeSupport) could be associated with other tables (eg referral aspects) for finer control - enabling the same 'feature' multiple times
    // we would like to use lists, but hibernate doesn't like doing multiple lists in one query (http://stackoverflow.com/questions/6562673/onetomany-list-vs-set-difference)
    // support / threat / service implies this servicetype can have all at the same time
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "servicetypes_outcomesupports", joinColumns = @JoinColumn(name = "servicetypeId"), inverseJoinColumns = @JoinColumn(name = "outcomeId"))
    private Set<OutcomeSupport> outcomeSupports = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "servicetypes_outcomethreats", joinColumns = @JoinColumn(name = "servicetypeId"), inverseJoinColumns = @JoinColumn(name = "threatoutcomeId"))
    private Set<OutcomeThreat> outcomeThreats = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "servicetypes_flagthreats", joinColumns = @JoinColumn(name = "servicetypeId"), inverseJoinColumns = @JoinColumn(name = "flagDefId"))
    private Set<ListDefinitionEntry> flagThreats = new HashSet<ListDefinitionEntry>();

    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "servicetypes_questiongroups", joinColumns = @JoinColumn(name = "servicetypeId"), inverseJoinColumns = @JoinColumn(name = "questiongroupId"))
    private Set<QuestionGroupSupport> questionGroupsSupport = new HashSet<QuestionGroupSupport>();

    @Transient
    private ImmutableMap<Long, ServiceType_TaskDefinition> taskDefinitionsById;

    @Transient
    private ImmutableMap<String, ServiceType_TaskDefinition> taskDefinitionsByName;

    public ServiceType() {
        // needed for javassist
    }

    /**
     * This was introduced with central processing, with the intention that we know whether referrals on this servicetype
     * are the children of central processing referrals. This is used for recording data accordingly.
     *  - hiding child referrals in the 'referrals -> list' menu, @see ReferralListFilter, @see ServiceFilter
     *  - service-config-domain.ts ConfigResolverDefault which determines if central processing logic is to be used
     *      - Deprecated since console.assert(!this.serviceType.isChildService(), "Child services no longer supported");
     * It was also intended for knowing which services to list in 'allocate to services' - but we now use a JSON property
     * client side for that, allocateToServices.
     */

    public Optional<ServiceType_TaskDefinition> getTaskDefinitionById(long id) {
        if (this.taskDefinitionsById == null) {
            this.taskDefinitionsById = Maps.uniqueIndex(taskDefinitions, stra -> stra.getTaskDefinition().getId());
        }
        return Optional.ofNullable(this.taskDefinitionsById.get(id));
    }

    @Nullable
    public ServiceType_TaskDefinition getTaskDefinitionByName(String taskName) {
        if (this.taskDefinitionsByName == null) {
            this.taskDefinitionsByName = uniqueIndex(taskDefinitions, stra -> stra.getTaskDefinition().getName());
        }
        ServiceType_TaskDefinition taskDefinition = this.taskDefinitionsByName.get(taskName);
        if (taskDefinition == null) {
            log.warn("task {} not configured on ServiceType {}. Returning null", taskName, this.name);
        }
        return taskDefinition;
    }

    public boolean hasTaskDefinitionByType(@Nonnull TaskDefinition.Type type) {
        return taskDefinitions.stream().anyMatch(ra -> ra.getTaskDefinition().getType().equals(type));
    }

    public ServiceTypeMinimalView asMinimalView() {
        ServiceTypeMinimalView mv = new ServiceTypeMinimalView();
        mv.setId(getId());
        mv.setName(getName());
        mv.setVersion(getVersion());
        return mv;
    }

    public Optional<ServiceTypeWorkflow> getWorkflow() {
        return Optional.ofNullable(workflow);
    }

    /**
     * TODO: Consider returning a handle which can tell if it is Activiti or not
     * @return
     *      process key which may be the special internal {@link ServiceTypeWorkflow#LINEAR_WORKFLOW_PROCESS_KEY} */
    public @Nonnull String getWorkflowProcessKey() {
        if (workflow == null) {
            return ServiceTypeWorkflow.LINEAR_WORKFLOW_PROCESS_KEY + getId();
        }
        return workflow.getProcessKey();
    }

    public List<String> getWizardTasks() {
        return taskDefinitions == null
                ? ImmutableList.of()
                : taskDefinitions.stream().filter(isWizardTask())
                .map(stra -> stra.getTaskDefinition().getName())
                .collect(Collectors.toList());
    }
}
