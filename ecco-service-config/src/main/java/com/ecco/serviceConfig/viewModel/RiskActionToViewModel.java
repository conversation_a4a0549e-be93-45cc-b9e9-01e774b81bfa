package com.ecco.serviceConfig.viewModel;

import java.util.function.Function;

import javax.annotation.Nullable;

import com.ecco.serviceConfig.dom.Action;

public class RiskActionToViewModel implements Function<Action, RiskActionViewModel> {

    @Override
    @Nullable
    public RiskActionViewModel apply(@Nullable Action input) {
        if (input == null) {
            throw new NullPointerException("input Action must not be null");
        }

        RiskActionViewModel vm = new RiskActionViewModel();
        vm.name = input.getName();
        vm.id = input.getId().intValue();
        vm.orderby = input.getOrderby();
        vm.initialText = input.getInitialText();
        vm.disabled = input.isDisabled();
        vm.orderby = input.getOrderby();

        return vm;
    }

}
