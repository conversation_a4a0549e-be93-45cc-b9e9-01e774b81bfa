package com.ecco.serviceConfig.viewModel;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition;
import com.ecco.serviceConfig.dom.ServiceType_TaskDefinitionSetting;
import com.ecco.serviceConfig.dom.TaskDefinition;

public class TaskDefinitionEntryToViewModel implements Function<ServiceType_TaskDefinition, TaskDefinitionEntryViewModel> {

    @Override
    public TaskDefinitionEntryViewModel apply(ServiceType_TaskDefinition input) {
        TaskDefinitionEntryViewModel result = new TaskDefinitionEntryViewModel();
        TaskDefinition taskDefinition = input.getTaskDefinition();

        result.taskDefId = taskDefinition.getId();
        result.name = taskDefinition.getName();
        result.allowNext = input.isAllowNext();
        result.orderby = input.getOrderby();
        result.dueDateSchedule = input.getDueDateSchedule();

        Map<String, String> map = new HashMap<>();
        for (ServiceType_TaskDefinitionSetting setting : input.getSettings()) {
            if (setting.getOutcomeId() != null) {
                addToOutcomesMap(result, setting);
            }
            else {
                addToMap(map, setting);
            }
        }
        result.settings = map;

        return result;
    }

    private void addToOutcomesMap(TaskDefinitionEntryViewModel result, ServiceType_TaskDefinitionSetting setting) {
        if (result.outcomeSettings == null) {
            result.outcomeSettings = new HashMap<>();
        }
        var settings = result.outcomeSettings.computeIfAbsent(setting.getOutcomeId(), k -> new HashMap<>());
        addToMap(settings, setting);
    }

    private void addToMap(Map<String, String> map, ServiceType_TaskDefinitionSetting serviceType_taskDefinitionSetting) {
        if (map.put(serviceType_taskDefinitionSetting.getName(), serviceType_taskDefinitionSetting.getValue()) != null) {
            // find the sql with: select * from servicetypes_taskdefsettings group by serviceTypeId,taskdefinitionId,name having count(*) > 1;
            throw new IllegalStateException("Duplicate key");
        }
    }
}
