package com.ecco.serviceConfig.viewModel;

import java.util.function.Function;

import javax.annotation.Nonnull;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.Project;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ServiceCategorisationToViewModel implements Function<ServiceCategorisation, ServiceCategorisationViewModel> {

    private final ListDefinitionRepository listDefinitionRepository;

    @Nonnull
    @Override
    public ServiceCategorisationViewModel apply(@Nonnull ServiceCategorisation input) {

        ServiceCategorisationViewModel result = new ServiceCategorisationViewModel();
        result.id = input.getId();
        result.disabled = input.isDisabled();
        result.serviceId = input.getServiceId().intValue();
        result.serviceName = input.getService().getName();

        result.serviceTypeId = input.loadServiceTypeId();

        if (input.getProject() != null) {
            Project project = input.getProject();
            result.projectId = project.getId() == null ? null : project.getId();
            result.projectName = project.getName();

            if (project.getRegion() != null) {
                result.regionName = project.getRegion().getName();
                result.regionId = project.getRegion().getId().intValue();
            }
        }

        var clientGroupId = input.getClientGroupId();
        if (clientGroupId != null) {
            result.clientGroupId = clientGroupId;
            result.clientGroupName = listDefinitionRepository.findById(clientGroupId).get().getName();
        }

        var companyId = input.getCompanyId();
        if (companyId != null) {
            result.companyId = companyId;
            result.companyName = listDefinitionRepository.findById(companyId).get().getName();
        }

        var serviceGroupId = input.getServiceGroupId();
        if (serviceGroupId != null) {
            result.serviceGroupId = serviceGroupId;
            result.serviceGroupName = listDefinitionRepository.findById(serviceGroupId).get().getName();
        }

        result.buildingId = input.getBuildingId();

        return result;
    }
}
