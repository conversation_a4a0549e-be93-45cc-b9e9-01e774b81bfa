package com.ecco.repositories.repairs;

import com.ecco.dom.repairs.Repair;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.Optional;

public interface RepairRepository extends QuerydslPredicateExecutor<Repair>, CrudRepositoryWithFindOne<Repair, Integer> {

    @Query("SELECT i.serviceRecipient.id from Repair i where id = ?1")
    int getServiceRecipientId(int repairId);

    Optional<Repair> findRepairByServiceRecipientId(Integer id);
}
