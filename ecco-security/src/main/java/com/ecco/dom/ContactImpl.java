package com.ecco.dom;

import javax.persistence.*;
import javax.validation.constraints.Pattern;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.ObjectUtils;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorOptions;
import org.hibernate.annotations.Type;
import org.springframework.beans.factory.annotation.Autowired;

import com.ecco.infrastructure.entity.CodedEntity;
import com.ecco.infrastructure.hibernate.AntiProxyUtils;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.hibernate.HibTypeNames;
import com.ecco.security.event.LocationUpdated;
import com.ecco.dom.contacts.Address;
import com.ecco.dom.contacts.Contact;
import com.ecco.dom.upload.UploadedBytes;

import java.time.LocalDate;


// extend contactbase so we can have a contact be a member of an organisation - which is a contact
// see http://stackoverflow.com/questions/2422878/hibernate-parent-child-relationship-in-a-single-table
// use 'mappedsuperclass' to avoid hibernate not finding the table - see http://stackoverflow.com/questions/1666099/how-to-map-an-abstract-class-or-interface-in-hibernate-hql
// but then removed 'mappedsuperclass' to use 'discriminator'
// also needed 'entity' it seems - see http://stackoverflow.com/questions/1580907/hibernate-annotations-generating-query-that-produces-sqlgrammarexception
// we then needed 'table' to load the associated contacts from an action
// - this may well render some of the above obsolete
// this also seems interesting - http://stackoverflow.com/questions/1862457/hibernate3-self-referencing-objects

// we had mapped the entity-name ContactBase for use through the legacy med packages (and events etc)
// both this annotated version and the hbm can work together, in fact the ContactBase refers to User
// whereas we only refer to user here in ContactImpl in the Individual table
// we have since removed ContactBase hbm since we have removed the legacy code requiring it

@Entity(name = "Contact")
@Table(name="contacts")
@Inheritance(strategy=InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name="discriminator_orm", discriminatorType=DiscriminatorType.STRING)
// force use of correct discriminator where subclasses might get confused when loading
// see last comment on https://forum.hibernate.org/viewtopic.php?p=2343055&sid=119a0c5115055a0fc653b840e2a89dd2
@DiscriminatorOptions(force = true)
@Cacheable
@org.hibernate.annotations.Cache(usage= CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public abstract class ContactImpl extends CalendarableEntity implements IdName<Long>, CodedEntity, Contact {

    private static final long serialVersionUID = 4297991072428663458L;

    @Autowired
    @Transient
    private EntityUriMapper entityUriMapper;

    public void assignAddressProject(Long projectId) {
        if (projectId == null) {
            return;
        }
        // we are about to show an address page, spring will auto-create the address, but we do so here to assign
        if (address == null) {
            address = new Address();
        }
        address.setProjectId(projectId);
    }

    @Getter @Setter
    private String code;

    private transient Address previous_Address;
    @Embedded
    @Getter @Setter
    private Address address; // NB this is updated with addressLocation

    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="addressId")
    @Getter @Setter
    private AddressedLocation addressedLocation;

    // don't force things to be numeric
    //@Digits(fraction=0, integer=11, message="the phone number needs to be numeric")
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    @Column(name="phonenumber")
    @Getter @Setter
    private String phoneNumber;

    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    @Column(name="mobilenumber")
    @Getter @Setter
    private String mobileNumber;

    // We don't use @email as it's not much use!
    @Pattern(regexp = ".+@.+\\..+", message="the email is not valid")
    @Type(type=HibTypeNames.SEARCHABLE_ENCRYPTED_STRING)
    @Column
    @Getter @Setter
    private String email;

    @Column
    @Getter
    @Setter
    private LocalDate archived;

    // onetoone are normally eager
    @OneToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="avatarId")
    // needs to be Super because entity-name has no equivalent in annotations
    // https://forum.hibernate.org/viewtopic.php?t=992246
    @Getter @Setter
    private UploadedBytes avatar;

    @Override
    public abstract String getDisplayName();

    @Override
    public String getName() {
        return getDisplayName();
    }

    @Override
    protected void prePersist() {
        super.prePersist();
        copyAddressOnFirstSave();
    }

    protected void copyAddressOnFirstSave() {
        // This is for first save - to maintain the original address behaviour
        if (addressedLocation != null) {
            address = Address.from(addressedLocation);
        }
    }

    /** If a new address has been set via address and we have addressedLocation in use, then migrate what has
     * been entered in {@link #address} to a new {@link #addressedLocation}
     */
    @PreUpdate
    protected void preUpdate() {
        migrateAddressedLocations();
    }

    protected void migrateAddressedLocations() {
        if (address != null && addressedLocation != null && !addressedLocation.matches(address)) {
            throw new RuntimeException("AddressedLocation and Address do not match");
        }
    }

    @PostLoad
    @PostPersist
    protected void postLoadOrPostPersist() {
        storePreviousAddress();
    }

    protected void storePreviousAddress() {
        previous_Address = address == null ? null : address.copy();
    }

    /*
     * PostUpdate is after flush to the database, and 'after' tx commit (at least what tx managers are aware of)
     *      for breakpoints use AbstractPlatformTransactionManager.processCommit
     *      for breakpoints use TransactionSynchronizationUtils.trigger...
     * Therefore repository.save (or just relying on jpa dirty check) doesn't trigger this .publish, but would .publishAfterTx
     * However, the original code here does work since entityService.save/update does an explicit flush to trigger PostUpdate whilst in 'before' tx commit mode.
     *
     * So we need a solution that is part of the tx, ie 'before' commit which means we need a different annotation to PostUpdate.
     * PreUpdate should cover both repo.save, entityService.save/update, or no explicit save at all (relying on jpa dirty check)
     * but PreUpdate is also part of the 'after' commit cycle - requiring the .publishAfterTx.
     * So perhaps use .publishAfterTx, but that causes an issue with BulkPorpertyUpdater - tx not available.
     * TODO So we need to find another way to determine 'about to save' to trigger a .publish in 'before' tx phase.
     */
    @PostUpdate
    protected void postUpdate() {
        fireContactAddressUpdated();
    }

    private void fireContactAddressUpdated() {
        if (previous_Address != null) {
            if (!ObjectUtils.equals(address, previous_Address)) {
                // NB fired but not used since we now decorate the address rather then update a fixed number of appts
                final LocationUpdated event = new LocationUpdated(this, entityUriMapper.uriForEntity(this), ObjectUtils.defaultIfNull(address, "").toString());
                messageBus.publishBeforeTxEnd(event);
            }
        }
    }

    // use an instanceof so we can compare subtypes
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(ContactImpl.class.isAssignableFrom(obj.getClass()))) {
            return false;
        }
        ContactImpl other = (ContactImpl) obj;
        if (getId() == null) {
            if (other.getId() != null) {
                return false;
            }
        } else if (!getId().equals(other.getId())) {
            return false;
        }
        return true;
    }


    public Long getAvatarId() {
        return AntiProxyUtils.identifier(avatar);
    }

    public void inheritUnsetFieldsFrom(Company other) {
        if (email == null) { email = other.getEmail(); }
        if (phoneNumber == null) { phoneNumber = other.getPhoneNumber(); }
        if (mobileNumber == null) { mobileNumber = other.getMobileNumber(); }
        address.inheritUnsetFieldsFrom(other.getAddress());
    }

}
