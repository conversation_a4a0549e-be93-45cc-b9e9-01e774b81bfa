package com.ecco.security.service;

import com.ecco.dom.Individual;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.GroupAuthority;
import com.ecco.security.dom.User;
import org.joda.time.DateTime;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.provisioning.GroupManager;
import org.springframework.security.provisioning.UserDetailsManager;

import java.security.InvalidKeyException;
import java.util.List;
import java.util.Set;

/**
 * An interface which ties together the spring security {@link UserDetailsManager} and {@link GroupManager}
 * interfaces and adds Ecco-specific methods
 */
@WriteableTransaction
public interface UserManagementService extends UserDetailsManager, GroupManager {

    String CLIENTS = "client";

    @Override
    User loadUserByUsername(String username) throws UsernameNotFoundException;

    Long findUserId(String username);

    String findUsernameFromId(long userId);

    List<String> getAllUsernames();

    List<IndividualUserSummary> getAllUserIndividuals();

    /**
     * @deprecated use {@link #findIndividualsWithAuthority(String)}
     */
    @Deprecated
    List<String> findUsersWithAuthority(String authority);

    List<String> findUsersNotThirdParty();

    List<IndividualUserSummary> findIndividualsFromUsername(String username);

    List<IndividualUserSummary> findIndividualsFromContactId(int contactId);

    List<IndividualUserSummary> findIndividualsWithAuthority(String authority);

    List<IndividualUserSummary> findIndividualsWithGroups(String... groups);

    List<GroupAuthority> listGroupAuthorities();

    User generateClientUser(String displayName, Individual contact);

    void recordSuccessfulLoginTime(String username, DateTime time);

    void recordFailedLogin(Authentication authn);

    boolean isIpBlacklisted(String ip);

    void deleteExpiredPersistentLogins();

    User mergeNewGroups(User user, Set<String> groupNames);

    AuditUserSearchResult findUserRevisions(AuditUserSearchScope scope);

    List findRevisions(AuditSearchScope scope);

    Group findGroupByName(String groupName);

    String createPassword();

    String encodePassword(String rawPass, User user);

    boolean isPasswordValid(User user, String rawPass);

    boolean passwordMatchesEncoded(String rawPass, User user, String encoded);

    String generateMfaSecretKey();

    void registerMfa(String secretKey, User user);

    boolean validateMfa(String totp, User user) throws InvalidKeyException;
}
