package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.dom.incidents.Incident;
import com.ecco.webApi.incidents.CreateIncidentCommandViewModel;
import com.ecco.webApi.incidents.IncidentController;
import com.ecco.webApi.incidents.IncidentViewModel;
import com.ecco.webApi.viewModels.Result;
import org.hamcrest.Matchers;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;

import static org.springframework.hateoas.IanaLinkRelations.SELF;


public class IncidentActor extends BaseActor {
    private static final String incidentUri = WebApiSettings.APPLICATION_URL + "/api/incidents/";

    public IncidentActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /*public ResponseEntity<IncidentViewModel[]> findAll() {
        return restTemplate.getForEntity(
                apiBaseUrl + "incidents/all/",
                IncidentViewModel[].class);
    }*/

    public IncidentViewModel getIncidentById(int id) {
        String uri = incidentUri + id + "/";
        ResponseEntity<IncidentViewModel> response = restTemplate.getForEntity(uri, IncidentViewModel.class);
        return response.getBody();
    }

    public int createIncident(String name, LocalDate received) {
        var vm = new IncidentViewModel();
        //vm.setName(name);
        vm.setReceivedDate(received);

        vm.setServiceAllocationId(Incident.DEFAULT_SERVICE_ALLOCATION_ID);

        CreateIncidentCommandViewModel createVm = new CreateIncidentCommandViewModel(vm);
        ResponseEntity<Result> response = this.executeCommand(createVm);
        Assert.state(Matchers.equalTo(HttpStatus.CREATED).matches(response.getStatusCode()));
        Assert.state(response.getBody().getId() != null);

        return IncidentController.EXTRACT_ID_FN.apply(response.getBody().getLink(SELF.value()).getHref());

    }

}
