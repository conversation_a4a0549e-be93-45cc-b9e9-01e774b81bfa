package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.model.AppointmentScheduleImportViewModel;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.AgreementResource;
import com.ecco.webApi.contacts.WorkerJobViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.webApi.rota.ServiceRecipientAppointmentScheduleCommandDto;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

public class AppointmentScheduleHandler extends AbstractHandler<AppointmentScheduleImportViewModel> {

    protected static final String apiPath = "/api/";

    public AppointmentScheduleHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    protected void processEntity(ImportOperation<AppointmentScheduleImportViewModel> operation) {
        try {
            this.processDemandSchedule(operation);
        } catch (IllegalStateException | IllegalArgumentException e) {
            log.error("failed to import at row: " + operation.row, e);
        }
    }

    private void processDemandSchedule(ImportOperation<AppointmentScheduleImportViewModel> operation) {

        LocalDate end = null; // NB end is ignored on getOrCreateAgreement and cmd
        var startTime = LocalTime.of(7, 0);
        var duration = 15;
        var title = "-";

        var input = operation.record;
        var start = input.start;
        var rvm = getReferralFromCode(operation, input.referralCode);
        var avm = getOrCreateAgreement(rvm.serviceRecipientId, start, null);

        WorkerJobViewModel workerJob = getWorkerJobForName(input.workerFirstName, input.workerLastName);

        ServiceRecipientAppointmentScheduleCommandDto cmd = new ServiceRecipientAppointmentScheduleCommandDto(BaseCommandViewModel.OPERATION_ADD, rvm.getServiceRecipientId());
        cmd.agreementId = Objects.requireNonNull(avm.getAgreementId()).intValue();
        // eventRef;
        // serviceRecipientHandle; // passed from client but currently ignored as we have agreementId in URL
        cmd.title = ChangeViewModel.changeNullTo(title);
        cmd.startDate = ChangeViewModel.changeNullTo(JodaToJDKAdapters.localDateToJDk(start));
        // endDate
        // applicableFromDate;
        cmd.time = ChangeViewModel.changeNullTo(startTime);
        cmd.appointmentTypeId = ChangeViewModel.changeNullTo(input.categoryId != null ? input.categoryId : Integer.valueOf(6));
        cmd.resourceSrId = workerJob != null ? workerJob.getServiceRecipient().serviceRecipientId : null;
        cmd.durationMins = ChangeViewModel.changeNullTo(duration);
        // rateCardId;
        cmd.adHoc = Boolean.FALSE;
        cmd.additionalStaff = 0;
        if (input.taskSummary != null) {
            cmd.tasks = ChangeViewModel.changeNullTo(input.taskSummary);
        }
        // tasksDirect;
        var frequencyType = input.intervalType != null ? input.intervalType : "WK";
        cmd.intervalType = ChangeViewModel.changeNullTo(frequencyType); // "WK" | "MTH" | "QTR" | "BI" | "YR"
        if (frequencyType.equals("WK")) {
            // Array of numbers representing 1 SUNDAY.. 7 SATURDAY, the non-ISO days
            var daysIso = new ArrayList<Integer>();
            if (Boolean.TRUE.equals(input.Sun)) daysIso.add(1);
            if (Boolean.TRUE.equals(input.Mon)) daysIso.add(2);
            if (Boolean.TRUE.equals(input.Tues)) daysIso.add(3);
            if (Boolean.TRUE.equals(input.Wed)) daysIso.add(4);
            if (Boolean.TRUE.equals(input.Thurs)) daysIso.add(5);
            if (Boolean.TRUE.equals(input.Fri)) daysIso.add(6);
            if (Boolean.TRUE.equals(input.Sat)) daysIso.add(7);
            AddedRemovedDto<Integer> days = new AddedRemovedDto<>();
            days.added = daysIso;
            cmd.days = days;
            cmd.intervalFrequency = ChangeViewModel.changeNullTo(input.intervalFrequency != null ? input.intervalFrequency : 1);
        }

        commandActor.executeCommand(cmd);
    }

    private WorkerJobViewModel getWorkerJobForName(String firstName, String lastName) {
        var contacts = this.contactActor.getAllIndiviudals(firstName, lastName).getBody();
        assert contacts != null;
        if (contacts.length != 1) {
            return null;
        }
        List<Long> ids = Arrays.stream(contacts).map(i -> i.contactId).toList();
        var workerJobs = workerActor.findWorkerJobsEmployedAtByContactIds(LocalDate.now(), ids).getBody();
        assert workerJobs != null;
        if (workerJobs.length != 1) {
            return null;
        }
        return workerJobs[0];
    }

    private AgreementResource getOrCreateAgreement(Integer serviceRecipientId, org.joda.time.LocalDate start, org.joda.time.LocalDate end) {
        var existing = agreementActor.getActiveAgreementsOnDate(serviceRecipientId, start.toDateTimeAtStartOfDay());
        if (existing.size() > 0) {
            return existing.get(0);
        }
        agreementActor.createAgreement(serviceRecipientId, start, end, null).getId();
        return agreementActor.getFirstAgreement(serviceRecipientId, start.toDateTimeAtStartOfDay());
    }

}
