package com.ecco.acceptancetests.ui;


import com.ecco.acceptancetests.Constants;
import com.ecco.acceptancetests.api.referral.ReferralStepsWebApi;
import com.ecco.acceptancetests.steps.*;
import com.ecco.data.client.actors.*;
import com.ecco.acceptancetests.ui.steps.webdriver.UIFactory;
import com.ecco.test.support.UniqueDataService;
import com.ecco.data.client.WebApiSettings;

import org.eeichinger.testing.web.BaseWebDriverTest;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.client.RestTemplate;

/**
 * Base class for test cases
 */
@RunWith(SpringJUnit4ClassRunner.class) // for @IfProfileValue
@TestExecutionListeners // Ensure no autowiring is attempted.
public abstract class BaseSeleniumTest extends BaseWebDriverTest {

    protected final String apiBaseUrl = WebApiSettings.APPLICATION_URL + "/api/";

    protected final RestTemplate restTemplate = restClient.template();
    protected final ServiceTypeActor serviceTypeActor = new ServiceTypeActor(restTemplate);

    protected AdminSteps adminSteps;
    protected CalendarSteps calendarSteps;
    protected HrSteps hrSteps;
    protected LoginSteps loginSteps;
    protected OfflineContext offlineSteps;
    protected GroupSupportContext groupSupportSteps;
    protected QUnitUI qUnitUI;
    protected RiskManagementContext riskManagementSteps;
    public ReferralSteps referralSteps;
    protected ReportLoginAuditSteps reportLoginAuditSteps;
    protected ReportListSteps reportListSteps;
    protected RotaSteps rotaSteps;
    protected SettingsSteps settingsSteps;
    protected SupportPlanContext supportPlanSteps;
    protected UserManagementSteps userManagementSteps;

    private final UniqueDataService unique = UniqueDataService.instance;

    protected final BaseCommandActor commandActor = new BaseActor(restTemplate);
    protected BuildingActor buildingActor = new BuildingActor(restTemplate);
    protected CalendarActor calendarActor = new CalendarActor(restTemplate);
    protected WorkerActor workerActor = new WorkerActor(restTemplate);
    public ClientActor clientActor = new ClientActor(restTemplate);
    protected final ContactActor contactActor = new ContactActor(restTemplate);
    public ReferralActor referralActor = new ReferralActor(restTemplate);
    public GroupSupportActor groupSupportActor = new GroupSupportActor(restTemplate);
    public AgreementActor agreementActor = new AgreementActor(restTemplate);
    public RotaActor rotaActor = new RotaActor(restTemplate);
    public ListDefActor listDefActor = new ListDefActor(restTemplate);
    public ServiceRecipientActor serviceRecipientActor = new ServiceRecipientActor(restTemplate);
    protected ServiceActor serviceActor = new ServiceActor(restTemplate);
    protected CacheActor cacheActor = new CacheActor(restTemplate);
    protected final SessionDataActor sessionDataActor = new SessionDataActor(restTemplate);
    protected final WorkflowActor workflowActor = new WorkflowActor(restTemplate);
    protected final UserActor userActor = new UserActor(restClient.getLastLoggedInUsernameSupplier(), restTemplate);
    public final ReferralSteps referralStepsWebApi = new ReferralStepsWebApi(restTemplate, referralActor, agreementActor,
            workflowActor, sessionDataActor, clientActor, contactActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor);
    protected UpdateTextCommandActor updateTextCommandActor = new UpdateTextCommandActor(restTemplate);

    protected BaseSeleniumTest() {
    }

    public void loginAsSysadminApi() {
        restClient.login(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);
    }
    public void loginAsUserApi(String user) {
        restClient.login(unique.userNameFor(user), unique.passwordFor(user));
    }
    public void logoutApi() {
        restClient.logout();
    }

    @Before
    public void beforeTest() {
        adminSteps = UIFactory.getInstance().getAdminUI();
        calendarSteps = UIFactory.getInstance().getCalendarUI();
        hrSteps = UIFactory.getInstance().getHrUI(workerActor);
        loginSteps = UIFactory.getInstance().getLoginUI();
        groupSupportSteps = UIFactory.getInstance().getGroupSupportUI();
        referralSteps = UIFactory.getInstance().getReferralUI();
        supportPlanSteps = UIFactory.getInstance().getSupportPlanUI();
        riskManagementSteps = UIFactory.getInstance().getRiskManagementUI();
        offlineSteps = UIFactory.getInstance().getOfflineUI();
        reportLoginAuditSteps = UIFactory.getInstance().getReportUserAuditUI();
        reportListSteps = UIFactory.getInstance().getReportListUI();
        userManagementSteps = UIFactory.getInstance().getUserManagementUI(userActor);
        settingsSteps = UIFactory.getInstance().getSettingsUI();

        rotaSteps = UIFactory.getInstance().getRotaUI(restTemplate, sessionDataActor, workerActor,
                agreementActor, referralActor, calendarActor,
                buildingActor, rotaActor, serviceRecipientActor,
                serviceActor, hrSteps, referralSteps);

        qUnitUI = UIFactory.getInstance().getQUnitUI();

        resetData(); // want to make this beforeClass, or not at all, and instead allow tests to be independent where possible so they can run together

    }

    @AfterClass
    public static void closeAllInstances() {
        UIFactory.getInstance().closeAllInstances();
    }

    /**
     * Use this to make an HTTP call to your test support webapp which can do some simple JdbcTemplate ops to clear tables containing
     * data that got created during your test.
     */
    protected void resetData() {
        // TODO: something like
        //        adminUI.navigateTestHarness("/clearTestData");
    }

    protected void login(String user) {
        if (user.equals("sysadmin")) {
            loginSteps.loginAsSysadmin();
            loginAsSysadminApi();
        } else {
            loginSteps.login(user);
        }
    }

    protected void logout() {
        loginSteps.logout();
        logoutApi();
    }
}
