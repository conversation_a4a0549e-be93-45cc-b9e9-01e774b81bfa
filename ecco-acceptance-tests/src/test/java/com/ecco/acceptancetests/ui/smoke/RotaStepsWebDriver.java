package com.ecco.acceptancetests.ui.smoke;

import com.ecco.acceptancetests.api.rota.RotaStepsWebApi;
import com.ecco.acceptancetests.steps.HrSteps;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.acceptancetests.steps.RotaSteps;
import com.ecco.acceptancetests.ui.steps.webdriver.WebDriverUI;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.actors.*;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.viewModels.Result;
import kotlin.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.openqa.selenium.WebDriver;
import org.springframework.web.client.RestTemplate;
import reactor.util.function.Tuple2;

import javax.annotation.Nonnull;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.ecco.data.client.ServiceOptions.DEMO_ALL;

public class RotaStepsWebDriver extends WebDriverUI implements RotaSteps {

    private final WorkerActor workerActor;
    private final HrSteps hrSteps;
    private final ReferralSteps referralSteps;
    private final RotaStepsWebApi rotaStepsAPI;

    public RotaStepsWebDriver(WebDriver webDriver, RestTemplate restTemplate, SessionDataActor sessionDataActor, WorkerActor workerActor,
                                 AgreementActor agreementActor, ReferralActor referralActor, CalendarActor calendarActor,
                                 BuildingActor buildingActor, RotaActor rotaActor,
                                 ServiceRecipientActor serviceRecipientActor, ServiceActor serviceActor,
                                 HrSteps hrSteps, ReferralSteps referralSteps) {
        super(webDriver);
        this.hrSteps = hrSteps;
        this.referralSteps = referralSteps;
        this.workerActor = workerActor;
        this.rotaStepsAPI = new RotaStepsWebApi(restTemplate, sessionDataActor, workerActor, agreementActor, referralActor, calendarActor,
                buildingActor, rotaActor, serviceRecipientActor, serviceActor, referralSteps);
    }

    @NotNull
    @Override
    public WorkerResult createWorkerAndJob(@Nonnull String username) {
        return rotaStepsAPI.createWorkerAndJob(username);
    }

    @NotNull
    @Override
    public WorkerResult createWorkerAndJobWithAvailabilityForToday(@Nonnull final String username, final Integer workerPrimaryLocationBuildingId) {
        Tuple2<Long, String> worker = hrSteps.createWorker(username);
        int workerJobId = hrSteps.createWorkerJob(worker.getT1(), LocalDate.now());
        var workerCalendarId = workerActor.getWorker(worker.getT1()).getBody().getCalendarId();
        hrSteps.linkWorkerToUser(worker.getT2(), username);
        var workerJob = workerActor.getWorkerJobId(workerJobId).getBody();
        assert workerJob != null;
        var workerResult = new WorkerResult(worker.getT1(), worker.getT2(), workerJobId, workerCalendarId, workerJob.getServiceRecipient().serviceRecipientId);
        return createWorkerAvailabilityForToday(workerResult, workerPrimaryLocationBuildingId);
    }
    private WorkerResult createWorkerAvailabilityForToday(WorkerResult worker, final Integer workerPrimaryLocationBuildingId) {
        if (workerPrimaryLocationBuildingId != null) {
            setWorkerPrimaryLocation(worker.getWorkerId().intValue(), worker.getWorkerJobId(), workerPrimaryLocationBuildingId);
        }
        String calendarId = workerActor.findWorkerCalendarId(worker.getWorkerId());
        rotaStepsAPI.createAvailabilityForToday(calendarId);
        return worker;
    }

    @NotNull
    @Override
    public Pair<Long, String> createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(@NotNull String appointmentTypeName, @NotNull LocalDateTime time, @Nullable Integer srId) {
        ReferralOptions options = new ReferralOptions().requiresProjects(false)
                .requiresDataProtection(true)
                .requiresEmergencyDetails(true);
//                .withServiceAgreement(agreementAppointmentType);
        Pair<Long, String> result = referralSteps.processReferral(options, DEMO_ALL, "FirstName", "LastName", "AB123456C", "this is a new referral comment 1");
        rotaStepsAPI.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(appointmentTypeName, result, time, srId);
        return result;
    }

    @Override
    public long createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(@Nonnull String appointmentTypeName, @Nonnull Pair<Long, String> referralResult, @Nonnull LocalDateTime time, Integer srId) {
        return rotaStepsAPI.createClientUnfulfilledAdhocAppointmentDemandCreateFirstAgreement(appointmentTypeName, referralResult, time, srId);
    }

    @Override
    public void setWorkerPrimaryLocation(long workerId, int workerJobId, int primaryLocationId) {
        rotaStepsAPI.setWorkerPrimaryLocation(workerId, workerJobId, primaryLocationId);
    }

    @Override
    public void checkCanAssignResourceToFirstAppointmentToday(@NotNull String resourceFilter, @NotNull String serviceRecipientDemandFilter, @NotNull String recipientName, @NotNull String resourceName) {
        rotaStepsAPI.checkCanAssignResourceToFirstAppointmentToday(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName);
    }

    @NotNull
    @Override
    public Result checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(@NotNull String serviceRecipientName, int serviceRecipientId, LocalDateTime time, Integer resourceSrId) {
        return rotaStepsAPI.checkCanCreateAdHocAppointmentOnFirstAgreementAtTime(serviceRecipientName, serviceRecipientId, time, resourceSrId);
    }

    @NotNull
    @Override
    public Result checkCanCreateScheduleOnFirstAgreement(@NotNull String serviceRecipientName, int serviceRecipientId, @Nullable LocalDateTime startDateTime, @Nullable DaysOfWeek daysOfWeek, LocalDate endDate, int additionalStaff, Integer resourceSrId) {
        return rotaStepsAPI.checkCanCreateScheduleOnFirstAgreement(serviceRecipientName, serviceRecipientId, startDateTime, daysOfWeek, endDate, additionalStaff, resourceSrId);
    }

    @Override
    public void checkClientReferralIsAvailableOffline(@NotNull String clientName) {
        rotaStepsAPI.checkClientReferralIsAvailableOffline(clientName);
    }

    @Nonnull
    @Override
    public FixedContainerViewModel createBuilding(@NotNull String buildingName) {
        return rotaStepsAPI.createBuilding(buildingName);
    }

    @Nonnull
    @Override
    public FixedContainerViewModel createBuilding(@NotNull String buildingName, @Nonnull FixedContainerViewModel parentBuilding) {
        return rotaStepsAPI.createBuilding(buildingName, parentBuilding);
    }

    @Nonnull
    @Override
    public FixedContainerViewModel createCareRun(@NotNull String careRunName, @Nonnull FixedContainerViewModel parentBuilding) {
        return rotaStepsAPI.createCareRun(careRunName, parentBuilding);
    }

    @Override
    public void createBuildingAgreement(@NotNull FixedContainerViewModel building) {
        rotaStepsAPI.createBuildingAgreement(building);
    }

    @Override
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(@NotNull FixedContainerViewModel building, @Nonnull LocalDateTime time) {
        rotaStepsAPI.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreement(building, time);
    }

    @Override
    public void createBuildingAvailabilityFromToday(int buildingId) {
        rotaStepsAPI.createBuildingAvailabilityFromToday(buildingId);
    }

    @Override
    public void checkCanAssignResourceToSingleAppointmentAtTime(@NotNull String resourceFilter, @NotNull String serviceRecipientDemandFilter, @NotNull String recipientName, @NotNull String resourceName, @NotNull LocalDateTime time) {
        rotaStepsAPI.checkCanAssignResourceToSingleAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time);
    }

    @Override
    public void checkCanUnAssignResourceFromSingleAppointmentAtTime(@NotNull String resourceFilter, @NotNull String serviceRecipientDemandFilter, @NotNull String recipientName, @NotNull String resourceName, @NotNull LocalDateTime time) {
        rotaStepsAPI.checkCanUnAssignResourceFromSingleAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time);
    }

    @Override
    public void checkCanAssignResourceToRecurringAppointmentAtTime(@NotNull String resourceFilter, @NotNull String serviceRecipientDemandFilter, @NotNull String recipientName, @NotNull String resourceName, @NotNull LocalDateTime time, @NotNull DaysOfWeek daysOfWeek, @NotNull LocalDate recurringEnd) {
        rotaStepsAPI.checkCanAssignResourceToRecurringAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd);
    }

    @Override
    public void checkCanUnAssignResourceFromRecurringAppointmentAtTime(@NotNull String resourceFilter, @NotNull String serviceRecipientDemandFilter, @NotNull String recipientName, @NotNull String resourceName, @NotNull LocalDateTime time, @NotNull DaysOfWeek daysOfWeek, @NotNull LocalDate recurringEnd) {
        rotaStepsAPI.checkCanUnAssignResourceFromRecurringAppointmentAtTime(resourceFilter, serviceRecipientDemandFilter, recipientName, resourceName, time, daysOfWeek, recurringEnd);
    }

    @Override
    public void createAppointmentTypeForBuilding(@NotNull String appointmentTypeName) {
        rotaStepsAPI.createAppointmentTypeForBuilding(appointmentTypeName);
    }

    @Override
    public void checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(@NotNull FixedContainerViewModel building, @NotNull String appointmentTypeName, @NotNull LocalDateTime time) {
        rotaStepsAPI.checkCanCreateBuildingUnfulfilledAdHocAppointmentDemandOnFirstAgreementOfTypeAtTime(building, appointmentTypeName, time);
    }

    @NotNull
    @Override
    public String createWorkerAndJobWithAvailabilityForToday(@NotNull String username, @NotNull String firstName, @NotNull String lastName, @Nullable Integer workerPrimaryLocationBuildingId) {
        return rotaStepsAPI.createWorkerAndJobWithAvailabilityForToday(username, firstName, lastName, workerPrimaryLocationBuildingId);
    }
}
