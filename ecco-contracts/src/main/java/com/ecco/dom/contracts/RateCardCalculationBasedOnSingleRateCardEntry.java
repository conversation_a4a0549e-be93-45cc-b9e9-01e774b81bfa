package com.ecco.dom.contracts;

import com.google.common.collect.Range;
import kotlin.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/**
 * Default implementation for choosing RateCard entries to charge by, and the calculation.
 * This assumes there is nothing smaller than a minute to charge by.
 */
@RequiredArgsConstructor
@Slf4j
public class RateCardCalculationBasedOnSingleRateCardEntry extends RateCardCalculationBase implements RateCardCalculation {

    @Override
    public Optional<RateCard> getRateCardInDate(@Nonnull Instant instant, @Nonnull List<RateCard> rateCards) {
        return getRateCardInDate(instant, rateCards, false);
    }

    @Override
    public List<RateCard> getRateCardsInDateRange(@Nonnull Range<Instant> range, @Nonnull List<RateCard> rateCards) {
        return super.getRateCardsInDateRange(range, rateCards);
    }

    @Override
    public List<RateCardEntry> determineRateCardEntries(@Nonnull RateCard rateCard, @Nullable Integer matchCategoryTypeId,
                                                        @Nullable Integer matchOutcomeId, @Nullable Collection<String> matchFactors) {

        return determineRateCardEntries(rateCard, matchCategoryTypeId, matchOutcomeId, matchFactors, true);
    }

    /**
     * Calculate the charge based on the rate card entry.
     * @param temporalActual It is assumed the temporalActual is supplied in minutes.
     */
    public BigDecimal calculateCharge(@Nonnull List<RateCardEntry> entries, @Nullable Integer temporalActual) {
        return this.calculateChargeFromSingleEntry(entries.get(0), new Pair<>(BigDecimal.ZERO, temporalActual)).component1();
    }

    private Pair<BigDecimal, Integer> calculateChargeFromSingleEntry(@Nonnull RateCardEntry entry, Pair<BigDecimal, Integer> chargeAndRemainingTime) {
        var entryChargeAndRemainingTime = this.calculateOneCharge(chargeAndRemainingTime, false, entry);
        var cumulativeChargeAndRemainingTime = new Pair<>(chargeAndRemainingTime.component1().add(entryChargeAndRemainingTime.component1()), entryChargeAndRemainingTime.component2());

        return entry.getChildRateCardEntry() != null
            ? this.calculateChargeFromSingleEntry(entry.getChildRateCardEntry(), cumulativeChargeAndRemainingTime)
            : cumulativeChargeAndRemainingTime;
    }

}
