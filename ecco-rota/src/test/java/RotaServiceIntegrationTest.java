import com.ecco.buildings.config.BuildingsConfig;
import com.ecco.calendar.core.*;
import com.ecco.calendar.sync.CalendarConfig;
import com.ecco.config.config.ConfigConfig;
import com.ecco.config.contracts.ContractsConfig;
import com.ecco.contacts.config.ContactsConfig;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.Individual;
import com.ecco.dom.Referral;
import com.ecco.dom.agreements.AppointmentTypeRepository;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.dom.hr.Worker;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.config.HrConfig;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.root.InfrastructureConfig;
import com.ecco.infrastructure.config.root.JmxConfig;
import com.ecco.infrastructure.config.root.Profiles;
import com.ecco.infrastructure.config.root.UTCConfig;
import com.ecco.infrastructure.time.Clock;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.AppContextConfig;
import com.ecco.rota.config.RotaConfig;
import com.ecco.rota.service.RotaDelegator;
import com.ecco.rota.service.RotaService;
import com.ecco.service.ReferralService;
import com.ecco.service.config.ServiceConfig;
import com.ecco.service.hr.HrService;
import com.ecco.serviceConfig.config.MessageSourceConfig;
import com.ecco.serviceConfig.config.ServiceConfigConfig;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.test.support.TestAppContextInitializer;
import com.ecco.test.support.UniqueDataService;
import com.ecco.upload.config.UploadConfig;
import com.ecco.workflow.WorkflowService;
import com.google.common.collect.Range;
import com.ecco.security.config.AclConfig;
import org.joda.time.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.net.URI;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;


/*
 * See CosmoCalendarSeriesIntegrationTest.
 * Created this to test a scenario with a split on a series calendar.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = TestAppContextInitializer.class,
        // full config to make it quicker/easier, but we don't have access to everything so we stub things in @Configuration below
        classes = {
                InfrastructureConfig.class, UTCConfig.class, JmxConfig.class, //LegacyConfig.class, SecurityConfig.class,
                MessageSourceConfig.class, ServiceConfig.class,
                HrConfig.class, ConfigConfig.class, ServiceConfigConfig.class, /*SubmissionConfig.class,*/ AclConfig.class,
                /*WorkflowConfig.class,*/ AppContextConfig.class, CalendarConfig.class, UploadConfig.class,
                /*GroupSupportConfig.class,*/ BuildingsConfig.class, RotaConfig.class, ContractsConfig.class, ContactsConfig.class,
                RotaServiceIntegrationTest.ServiceConfig.class
        })
// For mysql, change to DEV and use jvm args -Ddb.schema and -Dliquibase=CREATE (we don't want to enable tests on production 'UPDATE')
@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
//@ActiveProfiles({Profiles.DEV, Profiles.TEST_FIXTURE})
public class RotaServiceIntegrationTest {

    @PersistenceContext
    protected EntityManager entityManager;

    @Resource(name="seriesCalendarService")
    CalendarService calendarService;

    @Autowired
    RotaService rotaService;

    @Autowired
    RotaDelegator rotaDelegator;

    @Autowired
    ReferralService referralService;

    @Autowired
    HrService hrService;

    @Autowired
    ServiceCategorisationRepository serviceCategorisationRepository;

    @Autowired
    AppointmentTypeRepository appointmentTypeRepository;

    @Autowired
    ServiceAgreementRepository agreementRepository;

    @Autowired
    DemandScheduleRepository demandScheduleRepository;

    @Autowired
    CalendarRecurringSeriesService seriesService;

    @Autowired
    CalendarRecurringService calendarRecurringService; // CalendarRecurringSeriesDecorator

    @Autowired
    CalendarRecurringSeriesRepository calendarRecurringSeriesRepository;

    @Autowired
    WorkerJobRepository workerJobRepository;

    private final UniqueDataService unique = UniqueDataService.instance;
    private final Clock clock = Clock.DEFAULT;
    protected final DateTime now = clock.nowWithoutMillies();

    @Configuration
    public static class ServiceConfig {
        @Bean(name = "activitiWorkflowService")
        public WorkflowService activitiService1() {
            return Mockito.mock(WorkflowService.class);
        }
        @Bean(name = "activitiWorkflow")
        public WorkflowService activitiService2() {
            return Mockito.mock(WorkflowService.class);
        }
        @Bean
        public ApplicationProperties applicationProperties() {
            return Mockito.mock(ApplicationProperties.class);
        }
    }

    // double check that a new schedule has 0 attendees, but after an allocation and de-allocation, it has 1 (the client)
    @Test
    public void checkImpl_hasAttendeeAfterDeallocate() {

        int srId;
        String calendarIdDemand;
        var startSchedTime = Clock.DEFAULT.nowWithoutMillies().withDayOfMonth(1).withTimeAtStartOfDay();
        var startSched = startSchedTime.toLocalDate();

        // GIVEN a schedule from 1st forever
        {
            var r = createReferral("deallocated", "attendees", "deallocAttendee-check");
            calendarIdDemand = r.getClient().getContact().getCalendarId();
            srId = r.getServiceRecipientId();
            // every day forever
            createAgreementAndSchedule(r.getServiceRecipientId(), startSched);
        }

        // CHECK has no attendees
        Recurrence.RecurrenceHandle apt;
        {
            // check if there are any attendees on new appointments
            var justAMonth = new Interval(startSchedTime, startSched.plusMonths(1).minusDays(1).toDateTimeAtStartOfDay());
            var recurrences = calendarService.findRecurrencesList(calendarIdDemand, justAMonth);

            // TENTATIVE
            var statusForNewDemand = recurrences.stream().map(rec -> rec.getStatus())
                    .collect(Collectors.toSet());
            assertThat(statusForNewDemand, hasSize(1));
            assertThat(statusForNewDemand.iterator().next(), equalTo(Recurrence.Status.TENTATIVE));

            // NO ATTENDEES
            var attendeesForNewDemand = recurrences.stream()
                    .flatMap(rec -> calendarService.findRecurrenceAttendees(rec).stream().map(Attendee::getCalendarId))
                    .collect(Collectors.toSet());
            assertThat(attendeesForNewDemand, hasSize(0)); // 0 means not even the client is an attendee - but it is a demand with TENTATIVE status

            apt = recurrences.get(0).getRecurrenceHandle();
        }

        // WHEN we allocate and deallocate
        {
            var calendarIdResource = createWorker("allocated-worker", "deallocAttendee", startSched);
            URI updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, "deallocAttendee-alloc"));
            calendarService.confirmRecurrence(apt, updatedByModification, calendarIdResource);

            updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, "deallocAttendee-dealloc"));
            calendarService.unconfirmRecurrence(apt, updatedByModification, false, calendarIdResource);
        }

        // THEN CHECK AGAIN
        {
            var justAMonth = new Interval(startSchedTime, startSched.plusMonths(1).minusDays(1).toDateTimeAtStartOfDay());
            var recurrences = calendarService.findRecurrencesList(calendarIdDemand, justAMonth);

            // TENTATIVE
            var statusForNewDemand = recurrences.stream().map(Recurrence::getStatus)
                    .collect(Collectors.toSet());
            assertThat(statusForNewDemand, hasSize(1));
            assertThat(statusForNewDemand.iterator().next(), equalTo(Recurrence.Status.TENTATIVE));

            // NO ATTENDEES
            var attendeesAfterOperation = recurrences.stream()
                    .flatMap(rec -> calendarService.findRecurrenceAttendees(rec).stream().map(Attendee::getCalendarId))
                    .collect(Collectors.toSet());
            assertThat(attendeesAfterOperation, hasSize(1)); // 1 means it was allocated, but the deallocation has removed the worker
        }
    }

    // found a scenario where we have a recurring allocation for too long, so we need to split-recreate (no visible change) into smaller chunks
    // TEST:
    //  - schedule on 1st month every day forever
    //  - reschedule each appointment for first 6 months
    //  - allocate 4-7 months
    //  - 'recreate'/split at 5 months
    //  - deallocate in the 5th month
    //  - drop in the 8th month
    //  - check: start to recreate date + 2 months (includes all above) = has same number, with different handles before/after split date
    //  - check: reschedules are still 30 minutes, and after that are 10 minutes
    //  - check: only 2 attendees in the range (between 4 and 7 months), and none past the range
    @Test
    public void scenario_recurringAndSplitRecreate() {

        int srId;
        String calendarIdDemand;
        var startSchedTime = Clock.DEFAULT.nowWithoutMillies().withDayOfMonth(1).withTimeAtStartOfDay();
        var startSched = startSchedTime.toLocalDate();
        var uniqueFor = "allocated-recreate";
        // for 6 months, adjust to 30mins
        var rescheduleDuration = new org.joda.time.Duration(startSchedTime, startSchedTime.plusMonths(6));
        // at 5 months, we split and recreate
        var splitDateRecreate = startSchedTime.plus(rescheduleDuration).minusMonths(1).toLocalDate();
        // between 4 and 7 months, we have attendees
        var attendeeDateRange = new Interval(splitDateRecreate.minusMonths(2).toDateTimeAtStartOfDay(), splitDateRecreate.plusMonths(1).minusDays(1).toDateTimeAtStartOfDay());

        // GIVEN a schedule from 1st with no end
        // TENTATIVE
        {
            var r = createReferral("allocated", "recreate", uniqueFor);
            calendarIdDemand = r.getClient().getContact().getCalendarId();
            srId = r.getServiceRecipientId();
            // every day forever
            createAgreementAndSchedule(r.getServiceRecipientId(), startSched);
            // lets just see if there are any attendees on blank appointments
            // we want the start + 1 month
            var justAMonth = new Interval(startSchedTime, startSched.plusMonths(1).minusDays(1).toDateTimeAtStartOfDay());
            var recurrences = calendarService.findRecurrencesList(calendarIdDemand, justAMonth);
            var attendeesForNewDemand = recurrences.stream().filter(rec -> rec.getStart().isAfter(attendeeDateRange.getEnd()))
                    .flatMap(rec -> calendarService.findRecurrenceAttendees(rec).stream().map(Attendee::getCalendarId))
                    .collect(Collectors.toSet());
            assertThat(attendeesForNewDemand, hasSize(0)); // 0 means not even the client is an attendee - but it is a demand with TENTATIVE status
        }

        // AND we make a lot of concrete exceptions in first 6 months (15min to 30min duration)
        // RESCHEDULE / concrete appointments to make operations slow
        var rescheduleInterval = new Interval(startSchedTime, startSchedTime.plus(rescheduleDuration));
        // NB find recurrences is different to find entries from the calendar
        var recurrencesOriginalReschedule = calendarService.findRecurrencesList(calendarIdDemand, rescheduleInterval);
        {
            for (var r : recurrencesOriginalReschedule) {
                // modify the recurrences to create a concrete entry/modification
                // NB this creates a 'hasModifications' and 'modifiesitemid' in the cosmo_item entry
                URI updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, uniqueFor));
                calendarService.rescheduleRecurrence(r.getRecurrenceHandle(), uniqueFor, null, 30, updatedByModification, null);
            }
        }

        // AND we make a lot of concrete exceptions (attendees) just for fun
        // - spanning the last 2 months of RESCHEDULE appointments and 1 month after (between 4 and 7 months)
        // CONFIRM
        var calendarIdResource = createWorker("allocated-worker", "recreate", startSched);
        var recurrencesOriginalAttendees = calendarService.findRecurrencesList(calendarIdDemand, attendeeDateRange);
        {
            for (var r : recurrencesOriginalAttendees) {
                // modify the recurrences to create a concrete entry/modification
                // NB this creates a 'hasModifications' and 'modifiesitemid' in the cosmo_item entry
                URI updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, uniqueFor.concat("-attendees")));
                calendarService.confirmRecurrence(r.getRecurrenceHandle(), updatedByModification, calendarIdResource);
            }
        }

        // AND we deallocate one in the 6th month
        // DEALLOCATE
        DateTime deallocatedDt;
        {
            var deallocateApt = recurrencesOriginalAttendees.stream().filter(r -> r.getStart().isAfter(startSchedTime.plusMonths(5))).findFirst().get();
            deallocatedDt = deallocateApt.getStart();
            URI updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, uniqueFor.concat("-deallocate")));
            calendarService.unconfirmRecurrence(deallocateApt.getRecurrenceHandle(), updatedByModification, false, calendarIdResource);
        }

        // AND we drop in the 8th month
        // DROP
        {
            var dropInterval = new Interval(attendeeDateRange.getEnd(), attendeeDateRange.getEnd().plusWeeks(2));
            // NB find recurrences is different to find entries from the calendar
            var dropRecurrences = calendarService.findRecurrencesList(calendarIdDemand, dropInterval);
            var lastApt = dropRecurrences.stream().reduce((first, second) -> second).orElseThrow();
            rotaService.dropActivity(srId, lastApt.getRecurrenceHandle(), null);
        }

        // WHEN split-recreate from splitDateRecreate (at 5 months) with no end
        {
            var schedules = demandScheduleRepository.findAllByAgreementServiceRecipientId(srId);
            var schedule = schedules.get(0);
            Range<Instant> range = Range.atLeast(JodaToJDKAdapters.localDateToJDk(splitDateRecreate).atStartOfDay().toInstant(ZoneOffset.UTC));
            URI updatedByModification = URI.create(String.format("test://%s-%s/", calendarIdDemand, uniqueFor));
            calendarRecurringService.recreateRecurrencesInRange(schedule.getRecurringEntryHandle(), range, updatedByModification);
        }

        // THEN we should have the same data returned despite the split-recreate
        {
            // we want the start -> splitDateRecreate + 2 months (1 has attendees, 1 is just normal/original)
            var wholeInterval = new Interval(startSchedTime, splitDateRecreate.plusMonths(2).minusDays(1).toDateTimeAtStartOfDay());
            var recurrences = calendarService.findRecurrencesList(calendarIdDemand, wholeInterval);

            // TEST that we don't overlap - it's the same number
            int totalMinusDrop = (int) wholeInterval.toDuration().getStandardDays() - 1;
            assertThat(recurrences.size(), is(totalMinusDrop));

            // assert that recurrences up to splitDate have one handle
            var handlesPre = recurrences.stream().filter(r -> r.getStart().isBefore(splitDateRecreate.toDateTimeAtStartOfDay())).map(Recurrence::getRecurringEntryHandle).collect(Collectors.toSet());
            assertThat(handlesPre, hasSize(1));

            // assert that recurrences from the splitDate have one handle
            var handlesPost = recurrences.stream().filter(r -> r.getStart().isAfter(splitDateRecreate.toDateTimeAtStartOfDay())).map(Recurrence::getRecurringEntryHandle).collect(Collectors.toSet());
            assertThat(handlesPost, hasSize(1));

            // assert that pre/post handles are not the same - we should be from different series
            assertThat(handlesPre.iterator().next(), not(equalTo(handlesPost.iterator().next())));

            // assert that the managed by is the same schedule
            // TODO - only before the split?
            var managedBy = recurrences.stream().filter(r -> r.getStart().isBefore(splitDateRecreate.toDateTimeAtStartOfDay())).map(Recurrence::getManagedBy).collect(Collectors.toSet());
            assertThat(managedBy, hasSize(1));

            // assert that all reschedules are still 30 minutes
            var rescheduleEnds = rescheduleInterval.getEnd();
            var times30 = recurrences.stream().filter(r -> r.getStart().isBefore(rescheduleEnds)).map(r -> new Duration(r.getStart(), r.getEnd()).getStandardMinutes()).collect(Collectors.toSet());
            //var times10Dte = recurrences.stream().filter(r -> r.getStart().isBefore(rescheduleEnds)).filter(r -> new Duration(r.getStart(), r.getEnd()).getStandardMinutes() == 10L).map(r -> r.getStart()).collect(Collectors.toSet());
            //var times30Dte = recurrences.stream().filter(r -> r.getStart().isBefore(rescheduleEnds)).filter(r -> new Duration(r.getStart(), r.getEnd()).getStandardMinutes() == 30L).map(r -> r.getStart()).collect(Collectors.toSet());
            //var times10DteOrdered = times10Dte.stream().sorted().collect(Collectors.toList());
            //var times30DteOrdered = times30Dte.stream().sorted().collect(Collectors.toList());
            assertThat(times30, everyItem(is(30L)));
            // assert that after reschedules are original 10 minutes
            var times10 = recurrences.stream().filter(r -> r.getStart().isAfter(rescheduleEnds)).map(r -> new Duration(r.getStart(), r.getEnd()).getStandardMinutes()).collect(Collectors.toSet());
            assertThat(times10, everyItem(is(10L)));

            // assert that attendees are still there in the range (between 4 and 7 months)
            var attendeesInRange = recurrences.stream().filter(r -> r.getStart().isAfter(attendeeDateRange.getStart()) && r.getEnd().isBefore(attendeeDateRange.getEnd()))
                    .flatMap(r -> r.getAttendees().stream().map(Attendee::getCalendarId))
                    .collect(Collectors.toSet());
            assertThat(attendeesInRange, hasSize(2));
            assertThat(attendeesInRange, containsInAnyOrder(calendarIdResource, calendarIdDemand));

            // assert that attendees are still not there past the range
            var attendeesPastRange = recurrences.stream().filter(r -> r.getStart().isAfter(attendeeDateRange.getEnd()))
                    .flatMap(r -> calendarService.findRecurrenceAttendees(r).stream().map(Attendee::getCalendarId))
                    .collect(Collectors.toSet());
            assertThat(attendeesPastRange, hasSize(0)); // 0 means not just demand

            // check the deallocated appointment
            var deallocatedApt = recurrences.stream().filter(r -> r.getStart().isEqual(deallocatedDt)).findFirst().get();
            var attendeesForDeallocated = calendarService.findRecurrenceAttendees(deallocatedApt).stream().map(Attendee::getCalendarId).collect(Collectors.toSet());
            assertThat(attendeesForDeallocated, hasSize(1)); // 1 means it was allocated, but the deallocation has removed the worker
        }
    }

    // found a scenario where we have a recurring allocation, but on a split/edit the new schedule goes from the start of the previous
    @Test
    public void scenario_recurringAndSplit() {

        // GIVEN a schedule from 1st
        String calendarIdResource;
        int srId;
        var startSched = LocalDate.now().withDayOfMonth(1);
        {
            calendarIdResource = createWorker("first", "last", startSched);

            srId = createReferral("first", "last", "rota-test").getServiceRecipientId();

            createAgreementAndSchedule(srId, startSched);
        }

        // AND allocate from 17th - this creates 2 series entries (1st to 17th, and beyond)
        DemandSchedule schedule;
        {
            URI updatedBy = URI.create(String.format("test://%s-%s/", unique.nameFor("alloc-split"), "alloc"));
            Range<Instant> range = Range.atLeast(JodaToJDKAdapters.localDateToJDk(startSched.plusDays(17-1)).atStartOfDay().toInstant(ZoneOffset.UTC));
            var schedules = demandScheduleRepository.findAllByAgreementServiceRecipientId(srId);
            schedule = schedules.get(0);
            calendarService.confirmRecurrencesInRange(schedule.getRecurringEntryHandle(), range,
                    DaysOfWeek.from(List.of(1,2,3,4,5,6,7)), updatedBy, null,  null, calendarIdResource);
        }

        /*
        // AND deallocate example
        {
            URI updatedBy = URI.create(String.format("test://%s-%s/", unique.nameFor("alloc-split"), "dealloc"));
            Range<Instant> range = Range.atLeast(JodaToJDKAdapters.localDateToJDk(startSched.plusWeeks(4)).atStartOfDay().toInstant(ZoneOffset.UTC));
            calendarService.unConfirmRecurrencesInRange(schedule.getRecurringEntryHandle(), range,
                    DaysOfWeek.from(List.of(1,2,3,4,5,6,7)), updatedBy, calendarIdResource);
            // TEST series
            // seriesrelated should have one entry, since a series is only recorded when it needs splitting (deallocate here), and even then,
            // the entry represents the latter split point where the parent represents the first half
            var seriesEntries = calendarRecurringSeriesRepository.findAllBySeriesHandle(schedule.getRecurringEntryHandle());
            assertThat(StreamSupport.stream(seriesEntries.spliterator(), false).count(), is(1L));
        }
        */

        /*
        // AND drop example
        {
            // find a weeks' worth of recurrences from Tues
            var interval = new Interval(startSched.plus, start.plusWeeks(1).minusDays(1));
            var recurringEntry = calendarIdDemand_recurringEntry.second;
            var intervalFromTues = new Interval(start.plusDays(1), start.plusWeeks(1).minusDays(1));
            var dropFirstTues = calendarService.findFirstRecurrence(recurringEntry.getHandle(), intervalFromTues);
            calendarService.dropRecurrence();
        }
        */

        // WHEN split from 23rd
        {
            var definition = RecurringEntryChangeDefinition.BuilderFactory.create()
                    .applicableFromDate(startSched.plusDays(23-1))
                    .daysAdded(List.of(1,2,3,4,5,6,7))
                    .build();
            rotaService.splitAppointmentSchedule(schedule, definition);
        }

        // THEN we shouldn't have double apt showing before the split, which we are
        // because loadResource=true seems to think they are allocated also when we are not
        //      eg http://localhost:8888/ecco-war/api/rota/workers:all/view/?startDate=2022-05-16&endDate=2022-05-22&serviceRecipientFilter=referrals%3Aall&loadResource=true&loadDemand=false
        // and this is because our 'split' (edit schedule) at the time was just looping through each in the series and resetting the start/end
        //      because the schedule.truncate called 'calendarRecurringService.updateRecurringEntryBounds(getRecurringEntryHandle(), getStart(), getEnd());'
        //      which the decorator intercepted as looping through all entries regardless and applying the start/end
        // and so caused all the entries to be applicable across all the schedule.
        // The fix (61b29cdb) has been to ensure that the start date was not altered, but only the end date from null to 23rd (triggering 'endStatus == BOUND_STATUS.LOWER').
        // There was then further work by 39cd9df7 to not reply on the incoming handle as existing - because it may have been replaced by two entries - and
        // this involved looking up the series lower/upper, which in itself caused a NPE solved in 1217bbcc.
        {
            var handler = rotaDelegator.selectHandler("workers:all", "referrals:all");
            var rota = rotaService.fetchRota(handler, startSched, startSched.plusDays(6), "workers:all", "referrals:all", true, false);
            var resourceAppointments = rota.getResources().get(0).getAppointments();
            assertThat(resourceAppointments.size(), is(0));
        }

    }

    private void createAgreementAndSchedule(int srId, LocalDate startSched) {
        int agreementId;
        // agreement
        var agreement = new ServiceAgreement();
        agreement.setServiceRecipientId(srId);
        agreement.setStart(startSched);
        //noinspection unchecked
        agreement = agreementRepository.save(agreement);
        agreementId = agreement.getId().intValue();

        var aptId = appointmentTypeRepository.findAll().get(0).getId();

        // schedule
        URI updatedBy = URI.create(String.format("test://%s-%s/", unique.nameFor("alloc-split"), "created"));
        var definition = RecurringEntryChangeDefinition.BuilderFactory.create()
            .agreementId(agreementId)
            .applicableFromDate(startSched)
            .start(startSched)
            .startTime(LocalTime.MIDNIGHT.plusHours(11))
            .durationMins(10)
            .additionalStaff(0)
            .adHoc(false)
            .appointmentTypeId(aptId.intValue())
            .intervalType("WK")
            .intervalFrequency(1)
            .daysAdded(List.of(1,2,3,4,5,6,7))
            .build();
        rotaService.addOneAppointmentSchedule(definition, null, 0, updatedBy);
    }

    private Referral createReferral(String firstName, String lastName, String uniqueFor) {
        String calendarIdDemand;
        int srId;
        // referral
        var i = new Individual();
        i.setFirstName(firstName);
        i.setLastName(lastName);
        var c = new ClientDetail(i);
        c.setCode(unique.nameFor(uniqueFor));
        referralService.setNewClient(c);
        calendarIdDemand = c.getContact().getCalendarId();
        var svcCat = serviceCategorisationRepository.findAll().get(0);
        var r = new Referral();
        r.setClient(c);
        r.setServiceAllocation(svcCat);
        r.setCode(unique.nameFor(uniqueFor));
        referralService.setReferral(r);
        srId = r.getServiceRecipientId();
        return r;
    }

    private String createWorker(String firstName, String lastName, LocalDate startSched) {
        String calendarIdResource;
        // worker
        var wI = new Individual();
        wI.setFirstName(firstName);
        wI.setLastName(lastName);
        Worker w = new Worker();
        w.setContact(wI);
        hrService.createOrUpdateWorker(w);
        calendarIdResource = w.getCalendarId();

        WorkerJob j = new WorkerJob();
        j.setWorker(w);
        //j.setContractedWeeklyHours(input.getContractedWeeklyHours());
        j.setStartDate(JodaToJDKAdapters.dateTimeToJdk(startSched.toDateTimeAtStartOfDay()));
        var sc = serviceCategorisationRepository.getById(Worker.DEFAULT_SERVICE_ALLOCATION_ID);
        j.setServiceAllocation(sc);
        workerJobRepository.save(j);
        return calendarIdResource;
    }

    // TODO integration test for rotaService split with support/tasks

    // CLONE of CosmoCalendarIntegrationTest.verifyRecurrences
    private Set<Entry> verifyRecurrences(String calendarId, Interval interval, int counts, DateTime... contains) {
        Set<Entry> events = calendarService.findEntries(calendarId, interval).getEntries();
        assertThat(events.stream().map(Entry::getStart).collect(Collectors.toList()), hasItems(contains));
        // Ctrl+U:
        //      Arrays.stream(events.toArray()).map(Entry::getStart).sorted().toArray();
        //      Arrays.stream(events.toArray()).filter(s -> ((Entry) s).getStart().toString().startsWith("2022-06-02")).toArray();
        events.stream()
                .sorted(Comparator.comparing(Entry::getItemUid))
                .forEach(System.out::println);
        assertThat(events.size(), is(counts));
        return events;
    }

    // CLONE of CosmoCalendarIntegrationTest.createCalendar
    @SuppressWarnings("SameParameterValue")
    private String createCalendar(@Nonnull String uniqueFor) {
        CalendarOwnerDefinition.Builder cal = CalendarOwnerDefinition.BuilderFactory.create()
                .username(unique.userNameFor(uniqueFor))
                .email(unique.userNameFor(uniqueFor).concat("@email.com"))
                .password(unique.passwordFor(uniqueFor))
                .firstName(unique.firstNameFor(uniqueFor))
                .lastName(unique.lastNameFor(uniqueFor));
        return calendarService.createCalendar(cal.build());
    }

    // CLONE of CosmoCalendarIntegrationTest.createCalendar
    private RecurringEntryDefinition.Builder constructRecurringEntry(DateTime start, @Nonnull String uniqueFor) {
        Set<Integer> days = new HashSet<>(Arrays.asList(1,2,3,4,5,6,7));
        return RecurringEntryDefinition.BuilderFactory.create()
                .title(uniqueFor)
                //.managedByUri(URI.create("test://"+uniqueFor+"/"))
                .start(start)
                .scheduleEndDate(start.toLocalDate().plusWeeks(5).minusDays(1))
                .duration(Duration.standardMinutes(15))
                .intervalType("WK")
                .calendarDays(days)
                .intervalFrequency(1)
                .location(LocationDefinition.BuilderFactory.create().build());
    }

}
