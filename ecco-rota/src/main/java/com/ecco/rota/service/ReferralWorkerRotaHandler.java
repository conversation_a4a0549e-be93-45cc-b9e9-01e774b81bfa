package com.ecco.rota.service;

import static com.ecco.dao.DemandSchedulePredicates.getWherePredicateForDemandSchedulesOfServiceRecipient;
import static com.ecco.rota.service.WorkerResourceableRotaHandler.WorkerOption.*;
import static com.ecco.calendar.core.Recurrence.Status.DROPPED;
import static com.ecco.calendar.core.Recurrence.Status.TENTATIVE;

import java.util.EnumSet;
import java.util.List;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import com.ecco.dao.DemandSchedulePredicates;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.service.EventService;
import org.joda.time.DateTime;

import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.rota.webApi.dto.ServiceRecipientDemandDto;
import com.querydsl.core.types.dsl.BooleanExpression;

/**
 * Handles worker resource to referral demand.
 * This is a global rota 'whole organisation' - all workers (on the date) with all demand schedules (even buildings!)
 */
public class ReferralWorkerRotaHandler extends WorkerResourceableRotaHandler {

    ReferralWorkerRotaHandler() {
        this(null, null, null, null, null); // for cglib
    }

    public ReferralWorkerRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                     WorkerJobRepository workerJobRepository,
                                     ServiceAgreementRepository serviceAgreementRepository,
                                     RotaService rotaService,
                                     EventService eventService) {
        super(demandScheduleRepository, workerJobRepository, serviceAgreementRepository, rotaService, eventService);
    }


    @Override
    public boolean canHandle(String resourceFilter, String demandFilter) {
        return resourceFilter.startsWith("workers:") && demandFilter.startsWith("referrals:");
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        Rota rota = new Rota(params.getStartDate(), params.getEndDate(), params.getResourceFilter(), params.getDemandFilter(), params.getLoadResource(), params.getLoadDemand());

        var workerJobs = getResources(rota);
        return StreamSupport.stream(workerJobs.spliterator(), false).map(WorkerJob::getServiceRecipientId).toList();
    }

    @Override
    public void populateRota(Rota rota) {
        final Iterable<WorkerJob> workers = getResources(rota);

        // add all worker resources who are employed now, and include the worker's confirmed appts, availability, and alldayevents (also as an appt)
        if (rota.getLoadResource()) {
            addWorkersEntries(rota, workers, ALLOCATED_APPOINTMENTS, AVAILABILITY, EVENTS_WITH_CATEGORY);
        }

        if (rota.getLoadDemand()) {
            // add unallocated/dropped demand
            addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleWherePredicate(rota));
        }
    }

    private Iterable<WorkerJob> getResources(Rota rota) {
        Integer srId = getResourceServiceRecipientId(rota.getResourceFilter());
        var startDate = JodaToJDKAdapters.dateTimeToJdk(rota.getStartDate().toDateTimeAtStartOfDay());
        return srId != null
            ? workerJobRepository.findAllByServiceRecipient_IdIn(List.of(srId))
            : workerJobRepository.findAllWorkerJobsEmployedAt(startDate);
    }

    @Override
    protected BooleanExpression getDemandScheduleWherePredicate(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();

        var specificDemandSrId = getDemandServiceRecipientId(rota.getDemandFilter());
        return specificDemandSrId != null
            ? getWherePredicateForDemandSchedulesOfServiceRecipient(start, end, AppointmentSchedule.class, specificDemandSrId)
            : DemandSchedulePredicates.getWherePredicate(rota.getStartDate(), rota.getEndDate(), AppointmentSchedule.class);
    }
}
