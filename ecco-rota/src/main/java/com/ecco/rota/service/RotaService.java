package com.ecco.rota.service;

import java.io.Serializable;
import java.net.URI;
import java.time.Instant;
import java.util.EnumSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.ecco.calendar.core.*;
import com.ecco.dom.Individual;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.dto.AbstractHandle;
import com.ecco.rota.webApi.dto.*;
import com.google.common.collect.Range;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.joda.time.Interval;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;

import com.ecco.infrastructure.annotations.WriteableTransaction;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

@WriteableTransaction
public interface RotaService {

    /** Representation of a task instance directly associated with one schedule. */
    final class DirectTaskHandle extends AbstractHandle implements Serializable {

        private static final long serialVersionUID = 1L;

        DirectTaskHandle(String handle) {
            super(handle);
        }

        public static DirectTaskHandle fromUuid(UUID id) {
            return id != null ? new DirectTaskHandle(id.toString()) : null;
        }
        public static DirectTaskHandle fromString(String id) {
            return id != null ? new DirectTaskHandle(id) : null;
        }
        public static UUID getAsUuid(DirectTaskHandle h) {
            return UUID.fromString(h.handle);
        }
    }

    String findCalendarIdFromItemId(String itemId);

    /**
     * Wrap the method to apply a decorator.
     * {@link CalendarService#findRecurrences(RecurringEntryHandle, Range, Recurrence.Status)}
     */
    Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Range<Instant> interval, @Nullable Recurrence.Status status);

    /**
     * Find any repeating events in the calendar.
     */
    Stream<Recurrence> findRecurrencesFromCalendar(String calendarId, Interval interval);

    // No need to wrap this for a decorator currently, as ecco-rota doesn't use it
    //Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Interval interval, Recurrence.Status status);

    // No need to wrap this for a decorator currently, as RotaServiceImpl uses but doesn't return
    // Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Range<Instant> interval);

    // No need to wrap this for a decorator currently, as RotaServiceImpl uses but doesn't return
    //Stream<Recurrence> findModifiedRecurrences(RecurringEntryHandle handle, LocalDate equalOrAfter);

    /**
     * Wrapper for calendarService, so that we can remove calendarService references to encourage us to use rotaService.
     * Primarily because findRecurrences is decorating the return.
     * {@link CalendarService#findAvailability(String, Interval, boolean)}
     */
    Availability findAvailability(String calendarId, Interval interval, boolean baselineOnly);

    /**
     * @param startDate              - start date for the period of rota shown
     * @param endDate                - inclusive end date. So for a single day, endDate = startDate
     * @param demandedResourceFilter The type of resource being managed. e.g. "workers:all" or "resources:bed" or "resources:all"
     * @param serviceRecipientFilter If defined, filter to this recipient (e.g. "building:1234", "referral:100")
     */
    Rota fetchRota(RotaHandler handler, LocalDate startDate, LocalDate endDate, String demandedResourceFilter, String serviceRecipientFilter,
                   boolean loadResource, boolean loadDemand);

    /**
     * Split a single recurrence of an activity at the split time
     * This will do ... TBD what happens - does it create two recurrence exceptions in it's place?
     */
    void splitActivity(RecurrenceHandle recurrence, LocalTime splitPoint);

    void splitAppointmentSchedule(@Nonnull DemandSchedule origSchedule,
                                  RecurringEntryChangeDefinition scheduleChange,
                                  int serviceRecipientId,
                                  Individual author,
                                  DemandScheduleDirectTaskDefinitions tasksDirect);

    void splitAppointmentSchedule(@Nonnull DemandSchedule origSchedule,
                                  RecurringEntryChangeDefinition scheduleChange);

    void addOneAppointmentSchedule(@Nonnull RecurringEntryChangeDefinition scheduleChange,
                                   @Nullable DemandSchedule parent, int countRemaining,
                                   URI updatedBy);

    void addOneAppointmentSchedule(@Nonnull RecurringEntryChangeDefinition scheduleChange,
                                   @Nullable DemandSchedule parent, int countRemaining,
                                   URI updatedBy,
                                   int serviceRecipientId,
                                   @Nonnull Individual author,
                                   @Nonnull DemandScheduleDirectTaskDefinitions tasksChange);

    void rescheduleActivity(RecurrenceHandle recurrence, @Nullable String newTitle, @Nullable LocalDateTime newDateTime,
                            @Nullable Integer newDurationMins, URI updatedBy);

    void dropActivity(int serviceRecipientId, RecurrenceHandle recurrence, Integer reasonId);

    void reinstateActivity(RecurrenceHandle recurrence);

    void allocateAndMaybeRescheduleActivity(RotaHandler handler, @Nullable URI updatedBy, Integer allocateResourceId,
                                            RecurrenceHandle recurrenceHandle, java.time.LocalDateTime adjustedStart);

    /**
     * Demand is determined by the appointments within a DemandSchedule's recurring entry (put on the sr's calendarId - see DemandSchedule.PrePersist)
     * Get the demand schedule 'appointmentschedules' for the period and look in its calendar_entry_handle for recurrences with the right status.
     * Unallocated activities are events which are on the appointment schedule but the calendar event is not confirmed (and therefore, not allocated).
     *
     * @param params             the rota to which to add the unallocated activities (must contain valid start date as getDate())
     * @param recurrenceStatuses the statuses to include
     */
    void addDemandWithStatuses(RotaParams params, AppointmentCollection rota, EnumSet<Recurrence.Status> recurrenceStatuses,
                               BooleanExpression wherePredicate);

    /**
     * Allocated appointments are modifications of recurring events in CONFIRMED status, which correspond to having an
     * appointment schedule with a resource attendee. Confirmed occurrences are events with both calendarId's (resource and demand)
     * as attendees - see xHandler.allocateResource.
     */
    void addRelevantRecurrences(Recurrence recurrence, RotaResourceViewModel rotaEntry,
                                final Class<? extends DemandSchedule> entityClass,
                                @Nullable Integer serviceRecipientIdToUseAsAvailability);

    /**
     * Deallocate entries in the schedule.
     * Ideally we leave user modified events alone (via some MANAGED_BY_PROPERTY_NAME for system modified ones), but
     * that depends on the calendar service implementation.
     *
     * @return number of deallocations
     */
    int deallocateActivitiesFrom(RotaHandler handler, RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                 @Nullable DaysOfWeek matchDays, URI updatedBy,
                                 Integer deallocateResourceId);

    /**
     * Convenient way to get the schedule from the eventRef
     *
     * @param eventRef The same eventRef as used in RotaAppointmentViewModel and RecurringDemand (the recurring handle,
     *                 but can now be a recurrence handle)
     */
    DemandSchedule getAppointmentSchedule(String eventRef);

    DemandSchedule getAppointmentSchedule(Entry event);

    /**
     * Update the schedule directly, which means start date (not time) and end date - allowing us to change the bounds
     * of a recurring entry. Rarely used now, in favour for 'split'ting the schedule.
     * We decided that although editing other non-recurring elements was possible, but this is likely to be confusing from
     * a user experience perspective.
     */
    void updateAppointmentSchedule(DemandSchedule ds, RecurringEntryChangeDefinition dto,
                                   Integer serviceRecipientId, @Nonnull Individual author,
                                   DemandScheduleDirectTaskDefinitions tasksChange);

    /**
     * Find all the agreement srIds relevant for this resource filter, where the agreement is active between the dates.
     * Only used on the CreateAppointmentForm.js to display the list of agreements on the file.
     *
     * @param demandedResourceFilter The type of resource being managed. e.g. "workers:all" or "resources:bed" or "resources:all"
     * @param serviceRecipientFilter If defined, filter to this recipient (e.g. "building:1234", "referral:100")
     */
    List<Integer> findAllAgreementSrIdsByDemandAndScheduleDate(RotaHandler handler, String demandedResourceFilter, String serviceRecipientFilter,
                                                                    LocalDate startDate, LocalDate endDate);

    List<ServiceAgreement> findAllAgreementsByDemandAndScheduleDate(RotaHandler handler, String demandedResourceFilter, String serviceRecipientFilter,
                                                                    LocalDate startDate, LocalDate endDate);

}
