package com.ecco.webApi.rota;

import com.ecco.calendar.core.CalendarRecurringService;
import com.ecco.calendar.core.RecurringEntry;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.cachebust.EntityCacheBustKeyRepository;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.rota.optaplanner.conversion.ConversionKt;
import com.ecco.rota.optaplanner.domain.RotaProposal;
import com.ecco.rota.service.BaseRotaHandler;
import com.ecco.rota.service.RotaDelegator;
import com.ecco.rota.service.RotaService;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.support.EtaggedResponseCacheManager;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.Range;
import com.ecco.calendar.core.Recurrence;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import org.joda.time.LocalDate;
import org.optaplanner.core.api.solver.SolverFactory;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;

@RestController
@RequestMapping("/rota/{resourceFilter}")
public class RotaController extends BaseWebApiController {

    private final RotaService rotaService;
    private final CalendarRecurringService calendarRecurringService;
    private final RotaDelegator rotaDelegator;
    private final AppointmentActionCommandHandler appointmentActionCommandHandler;
    private final AppointmentRecurringActionCommandHandler appointmentRecurringActionCommandHandler;
    private final SolverFactory<RotaProposal> solverFactory = SolverFactory.createFromXmlResource("solverConfiguration.xml");
    private final EtaggedResponseCacheManager etaggedResponseCacheManager;
    private final EntityCacheBustKeyRepository entityCacheBustKeyRepository;
    private final EntityUriMapper entityUriMapper;

    public RotaController(
            RotaService rotaService,
            CalendarRecurringService calendarRecurringService,
            EntityUriMapper entityUriMapper,
            RotaDelegator rotaDelegator,
            AppointmentActionCommandHandler appointmentActionCommandHandler,
            AppointmentRecurringActionCommandHandler appointmentRecurringActionCommandHandler,
            EtaggedResponseCacheManager etaggedResponseCacheManager,
            EntityCacheBustKeyRepository entityCacheBustKeyRepository
    ) {
        super();
        this.rotaService = rotaService;
        this.calendarRecurringService = calendarRecurringService;
        this.entityUriMapper = entityUriMapper;
        this.rotaDelegator = rotaDelegator;
        this.appointmentActionCommandHandler = appointmentActionCommandHandler;
        this.appointmentRecurringActionCommandHandler = appointmentRecurringActionCommandHandler;
        this.etaggedResponseCacheManager = etaggedResponseCacheManager;
        this.entityCacheBustKeyRepository = entityCacheBustKeyRepository;
        solverFactory.getSolverConfig().getTerminationConfig().setUnimprovedSecondsSpentLimit(1L);
    }


    /**
     * Load a rota for the demandedResourceFilter (e.g. workers:all or resources:bed) for a given time-period,
     * optionally filtered to an entity like "building:1203"
     * <pre>
     *     /rota/workers:all/view?serviceRecipientFilter={default is referrals:all}&startDate={startDate}
     *     /rota/workers:all/view?serviceRecipientFilter=buildings:21&startDate={startDate}
     *     /rota/resources:bed/?startDate={startDate}
     * </pre>
     */
    @GetJson("/view") // If we cache this, we need to be able to flush it
    public Rota viewRota(
            WebRequest request,
            HttpServletResponse response,
            @PathVariable String resourceFilter,
            @RequestParam(required=false) String demandFilter,
            @RequestParam(required=false, defaultValue = "true") Boolean loadResource,
            @RequestParam(required=false, defaultValue = "true") Boolean loadDemand,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate endDate
    ) throws IOException {

        var start = startDate == null ? LocalDate.now() : startDate;
        var end = endDate == null ? start : endDate;

        Rota rota;
        Supplier<Rota> getRota = () -> getRota(resourceFilter, demandFilter, loadResource, loadDemand, start, end);

        // Allows method to be called with null request & response to just freshen the cache. and get the result
        // If we are doing it this way, then the return is useful, so we do a sleight of hand
        // and execute the rota early, and re-use the variable in the supplier so we can return and cache it
        // without re-calculating the supplier again
        if (request == null || response == null) {
            rota = getRota.get();
            getRota = () -> rota;
        } else {
            rota = null;
        }
        var handledCachedRequest = false;

        // handle demand caching (assumed not together)
        var demandSrId = BaseRotaHandler.getDemandServiceRecipientId(demandFilter);
        if (!loadResource && (loadDemand && demandSrId != null)) {
            var key = entityCacheBustKeyRepository.getTimestamp(BaseServiceRecipient.class, demandSrId) + "_df:" + demandFilter + "_" + start + "_" + end;
            // don't set a max-age on this, have the network round trip done each time (not the work, just network check), just in case sr has changed
            etaggedResponseCacheManager.getFromCacheWithEtagHandling(request, response,
                    CacheConfig.CACHE_ROTA_VIEW,
                    key,
                    0,
                    getRota);
            handledCachedRequest = true;
        }

        // handle resource caching (assumed not together)
        var resourceSrId = BaseRotaHandler.getResourceServiceRecipientId(resourceFilter);
        if (!loadDemand && (loadResource && resourceSrId != null)) {
            var key = entityCacheBustKeyRepository.getTimestamp(BaseServiceRecipient.class, resourceSrId) + "_rf:" + resourceFilter + "_" + start + "_" + end;
            // don't set a max-age on this, have the network round trip done each time (not the work, just network check), just in case sr has changed
            /*
            etaggedResponseCacheManager.getFromCacheWithEtagHandling(request, response,
                    CacheConfig.CACHE_ROTA_VIEW,
                    key,
                    0,
                    getRota);
            handledCachedRequest = true;
            */
            handledCachedRequest = false;
        }

        if (!handledCachedRequest) {
            etaggedResponseCacheManager.writeWithoutCache(response, getRota);
        }

        // Allows method to be called with null request & response to just freshen the cache. and get the result
        if (request == null || response == null) {
            return rota;
        }
        return null; // ignored because we've already written response. Not void return value as that messes with linkToApi(methodOn(..))
    }

    private Rota getRota(
            String resourceFilter, String demandFilter, Boolean loadResource, Boolean loadDemand, LocalDate start, LocalDate end
    ) {
        var handler = rotaDelegator.selectHandler(resourceFilter, demandFilter);
        return rotaService.fetchRota(handler, start, end, resourceFilter, demandFilter, loadResource, loadDemand);
    }


    /** Fetch matching service recipients (bldgs, workers, referrals) for the specified period.
     * Used for rota to then load a resource per srId */
    @GetJson("/resources/")
    public List<Integer> resources(
            @PathVariable String resourceFilter,
            @RequestParam(required=false) String demandFilter,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate endDate) {

        startDate = startDate == null ? LocalDate.now() : startDate;
        endDate = endDate == null ? startDate : endDate;
        var handler = rotaDelegator.selectHandler(resourceFilter, demandFilter);
        return handler.findAllResourceServiceRecipientIds(new RotaParams(startDate, endDate, resourceFilter, demandFilter));
    }

    @GetJson("/suggested-allocations")
    public RotaProposal suggestedAllocations(
            @PathVariable String resourceFilter,
            @RequestParam(required=false) String demandFilter,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) LocalDate endDate,
            @RequestParam(required=false) List<Integer> priorWeeksToMatch) {

        Rota rota = getRota(resourceFilter, demandFilter, true, true, startDate, endDate);

        List<Rota> rotasToMatch = getPriorRotas(resourceFilter, demandFilter, startDate, endDate, priorWeeksToMatch);

        RotaProposal input = ConversionKt.fromDto(rota, rotasToMatch);
        RotaProposal proposal = solverFactory.buildSolver().solve(input);
        return proposal;
    }

    private List<Rota> getPriorRotas(
            String demandedResourceFilter, String serviceRecipientFilter, LocalDate startDate, LocalDate endDate, List<Integer> priorWeeksToMatch
    ) {
        if (priorWeeksToMatch == null) {
            return emptyList();
        }
        return priorWeeksToMatch.stream()
                .map(week -> getRota(demandedResourceFilter, serviceRecipientFilter, true, true, startDate.minusWeeks(week), endDate.minusWeeks(week)))
                .collect(toList());
    }

    /** Find recurrences of a schedule that are confirmed */
    // Used for reporting - specifically fetchRelatedEntities with 'scheduleAptsConfirmedAtEnd'
    @GetJson("/schedule/{eventRef}/confirmed")
    public Stream<RotaAppointmentViewModel> findRecurrenceItemsConfirmed(
            @PathVariable String resourceFilter,
            @PathVariable String eventRef,
            @RequestParam @DateTimeFormat(iso = ISO.DATE) java.time.LocalDate startDate,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE) java.time.LocalDate endDate) {

        ZoneId utc = ZoneId.of("UTC");
        var endDateInstant = endDate != null ? endDate.plusDays(1).atStartOfDay(utc).toInstant() : Instant.now();
        Range<Instant> range = Range.closedOpen(startDate.atStartOfDay(utc).toInstant(), endDateInstant);

        return findRecurrenceItems(eventRef, range, Recurrence.Status.CONFIRMED, false);
    }

    /**
     * Find recurrences of a schedule
     * NB We could use {status} PathVariable direct to enum, but may be better with an uppercase converter: https://www.baeldung.com/spring-enum-request-param
     */
    // UNUSED - but compliments the above findRecurrenceItemsConfirmed
    @GetJson("/schedule/{eventRef}/")
    public Stream<RotaAppointmentViewModel> findRecurrenceItemsAll(
            @PathVariable String eventRef,
            @RequestParam @DateTimeFormat(iso = ISO.DATE_TIME) java.time.LocalDateTime startDateTime,
            @RequestParam(required=false) @DateTimeFormat(iso = ISO.DATE_TIME) java.time.LocalDateTime endDateTime) {

        ZoneId utc = ZoneId.of("UTC");
        var endDateInstant = endDateTime != null ? endDateTime.atZone(utc).toInstant() : Instant.now();
        Range<Instant> range = Range.closedOpen(startDateTime.atZone(utc).toInstant(), endDateInstant);

        return findRecurrenceItems(eventRef, range, null, true);
    }

    public Stream<RotaAppointmentViewModel> findRecurrenceItems(
            String eventRef,
            Range<Instant> range,
            Recurrence.Status status,
            boolean withAttendees) {

        var scheduleHandle = RecurringEntryHandle.fromString(eventRef);
        var recurrences = rotaService.findRecurrences(scheduleHandle, range, status);

        var schedule = rotaService.getAppointmentSchedule(scheduleHandle.toString());
        return recurrences.map(r -> new RotaAppointmentViewModel(schedule, r, withAttendees));
    }

    /**
     * Command handler end point.
     * Perform an action on an appointment.
     * NB possibly should be in an AppointmentController, as per AgreementController.
     */
    @PostJson("/appointment-action/{serviceRecipientId}")
    public Result appointmentAction(@PathVariable Integer serviceRecipientId,
                                    @PathVariable String resourceFilter,
                                    @Nonnull Authentication authentication,
                                    @Nonnull @RequestBody String requestBody) throws IOException {
        AppointmentActionParams params = new AppointmentActionParams();
        params.setResourceFilter(resourceFilter);
        params.setServiceRecipientId(serviceRecipientId);
        var result = appointmentActionCommandHandler.handleCommand(authentication, params, requestBody);
        // TODO: Have we bust the srId cache?
        return result;
    }

    /**
     * Command handler end point.
     * Perform an action on a repeating appointment schedule.
     * NB possibly should be in an AppointmentController, as per AgreementController.
     */
    @PostJson("/appointment-recurring-action/{serviceRecipientId}")
    public Result appointmentRecurringAction(@PathVariable Integer serviceRecipientId,
                                    @PathVariable String resourceFilter,
                                    @Nonnull Authentication authentication,
                                    @Nonnull @RequestBody String requestBody) throws IOException {
        AppointmentActionParams params = new AppointmentActionParams();
        params.setResourceFilter(resourceFilter);
        params.setServiceRecipientId(serviceRecipientId);
        return appointmentRecurringActionCommandHandler.handleCommand(authentication, params, requestBody);
    }

    /**
     * End point to perform back-end hidden recreating of a recurring appointment
     * to facilitate better performance of a schedule
     */
    @PostJson("/recreate/{recurringEntryHandle}/")
    public Result recreateRecurringAction(@PathVariable String recurringEntryHandle,
                                          @RequestParam @DateTimeFormat(iso = ISO.DATE) java.time.LocalDate splitDate) {

        ZoneId utc = ZoneId.of("UTC");
        Range<Instant> range = Range.atLeast(splitDate.atStartOfDay(utc).toInstant());
        var entryHandle = RecurringEntry.RecurringEntryHandle.fromString(recurringEntryHandle);
        URI uri = this.entityUriMapper.uriForEntity("recreate", splitDate);
        this.calendarRecurringService.recreateRecurrencesInRange(entryHandle, range, uri);
        return new Result("recreated " + recurringEntryHandle);
    }

}
