package com.ecco.webApi.messaging;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.config.repositories.TemplateRepository;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.config.service.TemplateService;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.messaging.EmailService;
import com.ecco.security.event.AppointmentNotificationEvent;
import com.ecco.calendar.core.Attendee;
import com.ecco.calendar.core.CalendarEntries;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Entry;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.Interval;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

import java.util.Locale;

import static java.util.Comparator.*;
import static java.util.stream.Collectors.joining;

@RequiredArgsConstructor
@Slf4j
public class CalendarNotificationAgent {
    private final CalendarService calendarService;
    private final EmailService emailService;
    private final SoftwareFeatureService featureService;
    private final TemplateRepository templateRepository;
    private final MessageBus<ApplicationEvent> messageBus;

    // NB currently this is manually triggered via EventController @GetJson("/notify")
    @Value("${enableEmailEventTrigger:false}")
    private boolean enableEmailEvent;

    @PostConstruct
    protected void init() {
        messageBus.subscribe(AppointmentNotificationEvent.class, this::notifyAppointments);
    }

    @RequiredArgsConstructor
    @Getter
    static public class Params {
        private final String tableRows;
    }

    private void notifyAppointments(AppointmentNotificationEvent event) {
        if (!featureService.featureEnabled("ecco.email")) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            //log.error("EMAIL NOT SENT - not enabled");
            return;
        }

        if (enableEmailEvent) {
            notifyAppointments(event.getEmail(), event.getCalendarId(), event.getInterval());
        }
    }

    private void notifyAppointments(String email, String calendarId, Interval interval) {

        if (!StringUtils.hasText(email)) {
            log.error("EMAIL NOT SENT - no email address");
            return;
        }

        try {
            final CalendarEntries entries = calendarService.findEntries(calendarId, interval);
            templateRepository.findById("NotifyAppointments").ifPresent(t -> {
                if (Boolean.TRUE.equals(t.getEnabled())) {
                    String body = CalendarNotificationAgent.generateEmailTemplate(entries);
                    log.info("EMAIL SENDING - to: {}\nSubject: Upcoming appointments\n\n{}", email, body);
                    emailService.sendMessage(email, "Upcoming appointments", body);
                }
            });
        } catch (Exception e) {
            log.error("EMAIL FAILED - failed to email contact");
        }

    }

    public static StringBuilder generateHead() {
        StringBuilder stringBuilder = new StringBuilder();

        return stringBuilder.append("<head>")
                .append("</head>")
                .append("<body>")
                .append("<table style=\"border-collapse: collapse;\" cellspacing=\"10\" cellpadding=\"10\">")
                .append("<tr>")
                .append("<th>start</th><th>end</th><th>title</th><th>attendees</th>")
                .append("</tr>");
    }

    public static void generateFooter(StringBuilder stringBuilder) {
        stringBuilder.append("</table></body>");
    }

    public static String generateEmailTemplate(CalendarEntries entries) {
        StringBuilder stringBuilder = generateHead();

        // "<th>start</th><th>end</th><th>title</th><th>attendees</th>"
        entries.getEntries().stream()
            .sorted(comparing(Entry::getStart).thenComparing(Entry::getEnd, nullsLast(naturalOrder())))
            .forEach(event -> {
                stringBuilder.append("<tr>");

                var start = DateTimeUtils.getFormattedDateTime(event.getStart(), Locale.UK);
                stringBuilder.append("<td>").append(start).append("</td>");
                var end = DateTimeUtils.getFormattedDateTime(event.getEnd(), Locale.UK);
                stringBuilder.append("<td>").append(end).append("</td>");

                stringBuilder.append("<td>").append(event.getTitle()).append("</td>");

                stringBuilder.append("<td>").append(event.getAttendees().stream().map(Attendee::getName).collect(joining(", "))).append("</td>");

                stringBuilder.append("</tr>");
            });

        generateFooter(stringBuilder);

        return stringBuilder.toString();
    }

}
