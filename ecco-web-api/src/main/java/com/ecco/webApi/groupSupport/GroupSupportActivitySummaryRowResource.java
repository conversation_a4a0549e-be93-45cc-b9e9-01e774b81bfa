package com.ecco.webApi.groupSupport;

import com.ecco.dao.GroupSupportActivitySummaryStats;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.hateoas.RepresentationModel;

import javax.annotation.Nullable;
import java.util.UUID;

import static lombok.AccessLevel.PROTECTED;

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public class GroupSupportActivitySummaryRowResource extends RepresentationModel<GroupSupportActivitySummaryRowResource> implements GroupSupportActivitySummaryStats {

    public Long id;
    public Integer serviceRecipientId;
    public UUID uuid;
    public String description;
    public LocalDateTime startDateTime;
    public Integer capacity;
    public Integer duration;
    public Integer activityTypeId;
    public Boolean course;
    public Long parentId;
    public Long childrenCount;

    @Override
    public void setClientsInvited(@Nullable Integer clientsInvited) {
        this.clientsInvited = clientsInvited;
    }

    @Override
    public void setClientsAttended(@Nullable Integer clientsAttended) {
        this.clientsAttended = clientsAttended;
    }

    @Override
    public void setClientsAttending(@Nullable Integer clientsAttending) {
        this.clientsAttending = clientsAttending;
    }

    /** optional **/
    @Nullable
    public Integer venueId;

    /** optional **/
    @Nullable
    public String venueName;

    /** optional **/
    @Nullable
    public Long serviceId;

    /** optional **/
    @Nullable
    public Long projectId;

    @Nullable
    public Integer clientsInvited;

    /** optional **/
    @Nullable
    public Integer clientsAttending;

    /** optional **/
    @Nullable
    public Integer clientsAttended;

    /** optional **/
    @Nullable
    public LocalDate endDate;

}
