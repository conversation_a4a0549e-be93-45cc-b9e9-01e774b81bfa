package com.ecco.webApi.groupSupport;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import com.ecco.dom.groupsupport.GroupSupportActivity;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

public class GroupActivityCommandViewModel extends BaseCommandViewModel {

    @Nonnull
    public static final String OPERATION_CREATE = "create";

    public String operation;

    public UUID activityUuid;

    public String discriminator_orm;

    public Boolean course;

    public Long parentId;

    @Nullable
    public ChangeViewModel<Integer> activityTypeId;

    @Nullable
    public ChangeViewModel<Integer> capacity;

    @Nullable
    public ChangeViewModel<String> description;

    @Nullable
    public ChangeViewModel<Integer> duration;

    @Nullable
    public ChangeViewModel<Long> projectId;

    @Nullable
    public ChangeViewModel<Long> serviceId;

    @Nullable
    public ChangeViewModel<LocalDateTime> startDateTime;

    @Nullable
    public ChangeViewModel<LocalDate> endDate;

    @Nullable
    public ChangeViewModel<Integer> venueId;


    public static GroupActivityCommandViewModel createSupport(UUID activityUuid) {
        return new GroupActivityCommandViewModel(OPERATION_CREATE, activityUuid, GroupSupportActivity.DISCRIMINATOR_SUPPORT);
    }



    GroupActivityCommandViewModel() {
        super();
    }

    GroupActivityCommandViewModel(String operation, UUID activityUuid, String discriminator_orm) {
        super("activities/commands/");
        this.operation = operation;
        this.activityUuid = activityUuid;
        this.discriminator_orm = discriminator_orm;
    }


    public GroupActivityCommandViewModel activityTypeIdChange(@Nullable Integer from, @Nullable Integer to) {
        this.activityTypeId = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel capacityChange(@Nullable Integer from, @Nullable Integer to) {
        this.capacity = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel dateChange(@Nullable LocalDateTime from, @Nullable LocalDateTime to) {
        this.startDateTime = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel endDateChange(@Nullable LocalDate from, @Nullable LocalDate to) {
        this.endDate = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel descriptionChange(@Nullable String from, @Nullable String to) {
        this.description = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel durationChange(@Nullable Integer from, @Nullable Integer to) {
        this.duration = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel projectIdChange(@Nullable Long from, @Nullable Long to) {
        this.projectId = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel serviceIdChange(@Nullable Long from, @Nullable Long to) {
        this.serviceId = ChangeViewModel.create(from, to);
        return this;
    }

    public GroupActivityCommandViewModel venueIdChange(@Nullable Integer from, @Nullable Integer to) {
        this.venueId = ChangeViewModel.create(from, to);
        return this;
    }
}
