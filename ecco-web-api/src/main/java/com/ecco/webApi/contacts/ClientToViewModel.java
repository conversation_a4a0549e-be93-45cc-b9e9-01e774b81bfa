package com.ecco.webApi.contacts;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.ClientDetail;
import com.google.common.base.Function;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public final class ClientToViewModel implements Function<ClientDetail, ClientViewModel> {
    private final ClientDetailAbstractToViewModel detailToViewModel = new ClientDetailAbstractToViewModel();
    private final PersonToViewModel personToViewModel;

    public ClientToViewModel(ListDefinitionRepository listDefinitionRepository) {
        this.personToViewModel = new PersonToViewModel(listDefinitionRepository);
    }

    @Nonnull
    @Override
    public ClientViewModel apply(@Nullable ClientDetail input) {
        if (input == null) {
            throw new NullPointerException("input ClientDetail must not be null");
        }

        ClientViewModel result = new ClientViewModel();

        personToViewModel.apply(input, result);

        detailToViewModel.apply(input, result);

        result.clientId = input.getId();
        result.code = input.getCode();
        result.externalSystemRef = input.getExternalClientRef();
        result.externalSystemSource = input.getExternalClientSourceName();

        // provide 'residenceId' and 'residenceName' where residenceName is used by ReferralOverviewControl.addressAsHtml
        // to show on the client file as 'residenceName @ parent line1' with the client address, where the client address
        // is set from the residenceId history in SRAddressLocationChangeCommandHandler.updateContactAddress
        // NB ReferralOverviewControl.addressAsHtml now simply shows '@' if residence is supplied, as the address is always correct
        if (input.getResidence() != null) {
            FixedContainer residence = input.getResidence();
            // residenceId is the actual address where we are located
            result.residenceId = residence.getId();
            // residenceName is the residenceId name
            // NB we can use getTopParent() to get additional hierarchical information
            result.residenceName = residence.getName();
        }

        result.housingBenefit = input.getHousingBenefit();

        return result;
    }
}
