package com.ecco.webApi.evidence;

import com.ecco.rota.webApi.dto.DemandScheduleDto;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.time.Instant;
import java.util.UUID;

import static lombok.AccessLevel.PROTECTED;

/**
 * The live status of an event (e.g. is someone attending, has associated work been started)
 */
@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
public final class CalendarEventSnapshotDtoResource extends RepresentationModel<CalendarEventSnapshotDtoResource> {

    // TODO check flow from controller to client dto

    @Nonnull
    public String eventUid;

    @Nullable
    public Integer serviceRecipientId;

    @Nullable
    public Integer serviceAllocationId;

    @Nullable
    public IndividualViewModel demandContact;

    @Nullable
    public IndividualViewModel resourceContact;

    @Nonnull
    public Instant plannedStartInstant;

    @Nonnull
    public Instant plannedEndInstant;

    @Nonnull
    public Long demandScheduleId;

    public DemandScheduleDto demandScheduleDto;

    public AddressViewModel plannedLocation;

    @Nullable
    public LocationViewModel location;

    @Nullable
    public Instant startInstant;

    @Nullable
    public Instant endInstant;

    @Nullable
    public Long contactId;

    @Nullable
    public UUID workUuid;
}
