package com.ecco.webApi.clients;

import com.ecco.dao.EvidenceFormWorkRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dao.ThreatWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.DeleteRequestEvidenceCommand;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.ecco.evidence.event.DeleteRequestEvidenceEvent;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.ecco.webApi.viewModels.DeleteRequestEvidenceCommandViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.UUID;


@Component
public class DeleteRequestEvidenceCommandHandler extends ServiceRecipientCommandHandler<
        DeleteRequestEvidenceCommandViewModel, DeleteRequestEvidenceCommand, DeleteEvidenceParams> {

    private final MessageBus<ApplicationEvent> messageBus;
    private final EvidenceSupportWorkRepository supportWorkRepository;
    private final EvidenceFormWorkRepository formWorkRepository;
    private final ThreatWorkRepository riskWorkRepository;
    private final ServiceRecipientRepository serviceRecipientRepository;

    @Autowired
    public DeleteRequestEvidenceCommandHandler(@Nonnull MessageBus<ApplicationEvent> messageBus,
                                               @Nonnull ObjectMapper objectMapper,
                                               @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                               @Nonnull EvidenceSupportWorkRepository supportWorkRepository,
                                               @Nonnull EvidenceFormWorkRepository formWorkRepository,
                                               @Nonnull ThreatWorkRepository riskWorkRepository,
                                               @Nonnull ServiceRecipientRepository serviceRecipientRepository) {
        super(objectMapper, serviceRecipientCommandRepository, DeleteRequestEvidenceCommandViewModel.class);
        this.messageBus = messageBus;
        this.supportWorkRepository = supportWorkRepository;
        this.formWorkRepository = formWorkRepository;
        this.riskWorkRepository = riskWorkRepository;
        this.serviceRecipientRepository = serviceRecipientRepository;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, DeleteEvidenceParams params,
                                           @NotNull DeleteRequestEvidenceCommandViewModel viewModel) {
        switch (viewModel.discriminator) {
            case "HR":
                throw new IllegalArgumentException("not implemented");
            case "SUPPORT":
                this.supportRequestDelete(viewModel.workUuid, viewModel.revoke);
                break;
            case "RISK":
                this.riskRequestDelete(viewModel.workUuid, viewModel.revoke);
                break;
            case "FORM":
                this.formRequestDelete(viewModel.workUuid, viewModel.revoke);
                break;
            default:
                throw new IllegalArgumentException("not implemented delete request handler for discriminator: " + viewModel.discriminator);
        }

        // If we aren't revoking, just pump out a task to approve/delete the work.
        // We don't manage the task after this, its in its own lifecycle - otherwise they should be
        // more tightly coupled for quicker searching and managing in the workflow system.
        // The task ui should check the latest status and will fail if a delete is
        // attempted when revoked (DeleteEvidenceAPITests.deleteEvidenceFailsWhenRequestRevoked).
        if (!viewModel.revoke) {
            ServiceRecipientSummary sr = this.serviceRecipientRepository.findOneSummary(viewModel.serviceRecipientId);
            DeleteRequestEvidenceEvent event = new DeleteRequestEvidenceEvent(this, viewModel.serviceRecipientId, sr.serviceAllocationId, viewModel.uuid);
            messageBus.publishBeforeTxEnd(event);
        }

        return null;
    }

    private void supportRequestDelete(UUID uuid, boolean revoke) {
        if (revoke) {
            this.supportWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).cancelDeleteRequest();
        } else {
            this.supportWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).requestDelete();
        }
    }

    private void formRequestDelete(UUID uuid, boolean revoke) {
        if (revoke) {
            this.formWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).cancelDeleteRequest();
        } else {
            this.formWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).requestDelete();
        }
    }

    private void riskRequestDelete(UUID uuid, boolean revoke) {
        if (revoke) {
            this.riskWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).cancelDeleteRequest();
        } else {
            this.riskWorkRepository.findById(uuid).orElseThrow(NullPointerException::new).requestDelete();
        }
    }

    @Override
    protected DeleteRequestEvidenceCommand createCommand(Serializable targetId, DeleteEvidenceParams params,
                                                         String requestBody,
                                                         DeleteRequestEvidenceCommandViewModel viewModel,
                                                         long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");
        Assert.state(params.workUuid.equals(viewModel.workUuid), "workUuid in body must match URI");

        return new DeleteRequestEvidenceCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody,
                params.serviceRecipientId, viewModel.evidenceGroup, viewModel.evidenceTask);
    }

}
