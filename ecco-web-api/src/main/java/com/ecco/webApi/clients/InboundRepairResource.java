package com.ecco.webApi.clients;

import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PROTECTED;


/**
 * External repair API - creates a repair without a login.
 * Available at /context/api/inbound/repairs/$schema/
 */

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
@Builder
public class InboundRepairResource extends RepresentationModel<InboundRepairResource> {

    @NotNull
    private Long serviceTypeId;

    @NotNull
    private Integer buildingId;

    @JsonSchemaMetadata(title = "category", order = 20)
    private Integer categoryId;

    private Integer rateId;

    @JsonSchemaMetadata(title = "priority", order = 25)
    private Integer priorityId;

}
