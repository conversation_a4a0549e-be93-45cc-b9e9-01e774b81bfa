package com.ecco.webApi.repairs;

import com.ecco.dom.repairs.Repair;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;

import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.function.Function;

public class RepairFromViewModel implements Function<RepairViewModel, Repair> {

    ServiceCategorisationRepository serviceCategorisationRepository;

    @PersistenceContext
    private EntityManager em;

    public RepairFromViewModel(ServiceCategorisationRepository serviceCategorisationRepository) {
        this.serviceCategorisationRepository = serviceCategorisationRepository;
    }

    @Override
    @Nullable
    public Repair apply(@Nullable RepairViewModel input) {
        if (input == null) {
            throw new NullPointerException("input RepairViewModel must not be null");
        }

        Repair j = new Repair();
        j.setId(input.getRepairId());
        j.setServiceTypeId(input.serviceTypeId);

        j.setReceivedDateJdk(input.getReceivedDate());
        if (input.getCategoryId() != null) {
            j.setCategoryId(input.getCategoryId());
        }
        j.setRateId(input.getRateId());
        if (input.getPriorityId() != null) {
            j.setPriorityId(input.getPriorityId());
        }
        // ignore signposting fields when creating a repair

        j.setBuildingId(input.getBuildingId());

        // FIXED service recipient allocation, but could come from the building OR type of fix
        var svcAllocId = Repair.DEFAULT_SERVICE_ALLOCATION_ID;
        var svcAlloc = em.getReference(ServiceCategorisation.class, svcAllocId);
        j.setServiceAllocation(svcAlloc);

        return j;
    }

}
