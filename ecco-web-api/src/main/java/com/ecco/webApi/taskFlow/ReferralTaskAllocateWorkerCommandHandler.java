package com.ecco.webApi.taskFlow;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.Individual;
import com.ecco.dom.Referral;
import com.ecco.evidence.event.WorkerAllocatedEvent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusUpdate;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Component
public class ReferralTaskAllocateWorkerCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskEditAllocateWorkerCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @Nonnull
    private final ReferralRepository referralRepository;

    @Autowired
    protected MessageBus<ApplicationEvent> messageBus;


    @Autowired
    public ReferralTaskAllocateWorkerCommandHandler(@Nonnull ObjectMapper objectMapper,
                                                    @Nonnull WorkflowTaskController workflowTaskController,
                                                    @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                    @Nonnull ReferralRepository referralRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditAllocateWorkerCommandViewModel.class);

        this.referralRepository = referralRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, ServiceRecipientTaskParams params,
                                               ReferralTaskEditAllocateWorkerCommandViewModel vm) {
        Referral r = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);

        ReferralTaskAllocateWorkerCommandHandler.handleAllocateWorker(auth, params, vm, r, messageBus, entityManager);

        referralRepository.saveAndFlush(r);
        return null;
    }

    public static void handleAllocateWorker(Authentication auth, ServiceRecipientTaskParams params,
                                       ReferralTaskEditAllocateWorkerCommandViewModel vm,
                                       ServiceRecipientCaseStatusUpdate r,
                                       MessageBus<ApplicationEvent> messageBus,
                                       EntityManager entityManager) {

        if (vm.allocatedWorkerContactId != null) {
            Long contactId = vm.allocatedWorkerContactId.to;
            Individual individual = contactId == null ? null : entityManager.getReference(Individual.class, contactId);
            r.setSupportWorker(individual);
            messageBus.publishBeforeTxEnd(new WorkerAllocatedEvent(vm, individual));
        }

    }
}
