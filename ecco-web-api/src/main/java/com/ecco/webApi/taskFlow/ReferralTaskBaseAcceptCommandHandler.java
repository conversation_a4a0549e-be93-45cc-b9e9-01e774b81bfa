package com.ecco.webApi.taskFlow;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.AgencyRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.ServiceRecipientContactRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.*;
import com.ecco.dom.incidents.IncidentServiceRecipient;
import com.ecco.dom.repairs.RepairServiceRecipient;
import com.ecco.event.ReferralClosedOff;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusUpdate;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.security.core.Authentication;

import javax.annotation.Nonnull;

import static java.lang.Boolean.TRUE;

public abstract class ReferralTaskBaseAcceptCommandHandler<VM extends ReferralTaskBaseAcceptCommandViewModel>
        extends ServiceRecipientTaskCommandHandler<VM> {

    @Nonnull
    private final ServiceRecipientRepository serviceRecipientRepository;
    @Nonnull
    private final ReferralRepository referralRepository;
    @Nonnull
    private final IncidentRepository incidentRepository;
    @Nonnull
    private final RepairRepository repairRepository;
    @Nonnull
    private final AgencyRepository agencyRepository;
    @Nonnull
    private final ServiceRecipientContactRepository serviceRecipientContactRepository;
    @Nonnull
    private final ListDefinitionRepository listDefinitionRepository;

    public ReferralTaskBaseAcceptCommandHandler(@Nonnull ObjectMapper objectMapper,
                                                @Nonnull WorkflowTaskController workflowTaskController,
                                                @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                @Nonnull Class<VM> vmClass,
                                                @Nonnull ServiceRecipientRepository serviceRecipientRepository,
                                                @Nonnull ReferralRepository referralRepository,
                                                @Nonnull IncidentRepository incidentRepository,
                                                @Nonnull RepairRepository repairRepository,
                                                @Nonnull AgencyRepository agencyRepository,
                                                @Nonnull ListDefinitionRepository listDefinitionRepository,
                                                @Nonnull ServiceRecipientContactRepository serviceRecipientContactRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, vmClass);
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.referralRepository = referralRepository;
        this.incidentRepository = incidentRepository;
        this.repairRepository = repairRepository;
        this.agencyRepository = agencyRepository;
        this.listDefinitionRepository = listDefinitionRepository;
        this.serviceRecipientContactRepository = serviceRecipientContactRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, ServiceRecipientTaskParams params,
                                               VM vm) {

        var sr = this.serviceRecipientRepository.findById(params.serviceRecipientId).get();

        ServiceRecipientCaseStatusUpdate srStatus = null;

        // REFERRAL
        if (sr.getPrefix().equals(ReferralServiceRecipient.PREFIX)) {
            // BEWARE: don't call any other ReferralRepository methods - update r and then save
            // We'd made this mistake on daysAttending
            srStatus = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);
        }
        if (sr.getPrefix().equals(IncidentServiceRecipient.PREFIX)) {
            srStatus = incidentRepository.findIncidentByServiceRecipientId(params.serviceRecipientId).get();
        }
        if (sr.getPrefix().equals(RepairServiceRecipient.PREFIX)) {
            srStatus = repairRepository.findRepairByServiceRecipientId(params.serviceRecipientId).get();
        }

        if (vm.acceptedState != null && vm.acceptedState.from != vm.acceptedState.to) {
            changeState(vm, srStatus);
        } else {
            updateState(vm, srStatus);
        }

        return null;
    }

    protected abstract void changeState(VM vm, ServiceRecipientCaseStatusUpdate r);
    protected abstract void updateState(VM vm, ServiceRecipientCaseStatusUpdate r);

    protected void signpost(VM vm, ServiceRecipientCaseStatusUpdate r, boolean signpostedAlready) {

        if (vm.signpostedComment != null) {
            r.withSignpostComment(vm.signpostedComment.to);
        }

        if (vm.signpostedAgency != null) {
            if (vm.signpostedAgency.to != null) {
                r.setSignpostedAgency(agencyRepository.getOne(vm.signpostedAgency.to));
                r.setSignpostedBack(false); // If setting an agency then must reset back
                saveAsContact(this.serviceRecipientContactRepository, r.getServiceRecipientId(), vm.signpostedAgency.to);
            }
            else {
                r.setSignpostedAgency(null);
            }
        }

        if (vm.signpostedBack != null) {
            if (TRUE.equals(vm.signpostedBack.to)) {
                r.setSignpostedBack(true);
                r.setSignpostedAgency(null); // reset agency if signposting back
            }
            else {
                r.setSignpostedBack(false);
            }
        }

        if (vm.signpostedReason != null) {
            r.setSignpostReason(vm.signpostedReason.to == null ? null : listDefinitionRepository.findById(vm.signpostedReason.to).orElse(null));
        }

        boolean signpostedNow = ServiceRecipientCaseStatusView.Support.isSignposted(r);
        if (!signpostedAlready && signpostedNow) {
            fireReferralClosedOff(r);
        }
    }

    private void fireReferralClosedOff(ServiceRecipientCaseStatusUpdate referral) {
        final ReferralClosedOff event = new ReferralClosedOff(referral, referral.getServiceRecipientId(), referral.getExitedDate(), referral.getDecisionMadeOnDT());
        messageBus.publishBeforeTxEnd(event);
    }

    // NB this is the referral contacts svcrec_contacts, not the svccat_contacts
    // the svccat_contacts is added to when Agency/Professionals are created
    public static void saveAsContact(ServiceRecipientContactRepository repository, int serviceRecipientId, long contactId) {
        ServiceRecipientContactId id = new ServiceRecipientContactId(serviceRecipientId, contactId);
        // no associations for now, but could do 'referrer'?
        //rc.getAssociatedTypeIds().add(Integer.valueOf(associatedTypeId));
        if (!repository.existsById(id)) {
            ServiceRecipientContact rc = new ServiceRecipientContact(id);
            repository.save(rc);
        }
    }
}
