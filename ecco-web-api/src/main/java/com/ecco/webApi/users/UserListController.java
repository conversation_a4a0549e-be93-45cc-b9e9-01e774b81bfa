package com.ecco.webApi.users;

import com.ecco.infrastructure.rest.hateoas.schema.*;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import com.ecco.webApi.viewModels.ResourceList;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

import static com.ecco.security.dom.QUser.user;
import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Web API to obtain a list of users, paged and filtered by lastname, status (enabled/disabled) and group.
 * This can then be used to build a rich client version of the user management list page.
 * The API is also self describing by means of a schema.
 */
@RestController
@RequestMapping("/users")
@Secured("ROLE_ADMINLOGIN")
public class UserListController extends SchemaProvidingController<UserListController> {
    private static final String REL_INSTANCES = "instances";

    @Value("${ecco.api.basePath:}")
    private String apiBasePath;

    private final UserRepository userRepository;
    private final UserListRowResourceAssembler rowResourceAssembler;


    public UserListController(UserRepository userRepository,
                              UserListRowResourceAssembler rowResourceAssembler) {
        super();
        this.userRepository = userRepository;
        this.rowResourceAssembler = rowResourceAssembler;
    }

    @Override
    public String getEntityTypeName() {
        return "users";
    }

    @GetJson("/")
    public ResourceList<UserListRowResource> list(
            @RequestParam(name = "search", required = false)
            @JsonSchemaMetadata(order = 3)
            String search,
            @RequestParam(name = "enabled", defaultValue = "true")
            @JsonSchemaMetadata(order = 10)
            @JsonSchemaProperty(enumValues = {
                    @EnumConstant(value = "false", title = "include disabled"),
                    @EnumConstant(value = "true", title = "enabled only"),
            })
            Boolean enabled,
            @RequestParam(name = "group", required = false)
            @JsonSchemaProperty(enumValues = {
                    @EnumConstant("staff"),
                    @EnumConstant("manager"),
                    @EnumConstant("senior manager"),
                    @EnumConstant("reports"),
                    @EnumConstant("useradmin"),
                    @EnumConstant("site sysadmin"),
                    @EnumConstant("sysadmin"),
                    @EnumConstant("hr"),
                    @EnumConstant("carer"),
                    @EnumConstant("finance"),
                    @EnumConstant("client"),
                    @EnumConstant("security")
            })
            @JsonSchemaMetadata(order = 20)
            String group,
            @RequestParam(name = "mfaRequired", required = false)
            @JsonSchemaMetadata(order = 15)
            @JsonSchemaProperty(enumValues = {
                    @EnumConstant(value = "true", title = "include mfa required"),
                    @EnumConstant(value = "false", title = "mfa not required"),
            })
            Boolean mfaRequired,
            @RequestParam(name = "page", defaultValue="0")
            Integer page,
            @RequestParam(name = "pageSize", defaultValue="15") // could do getPageSize("pageSize.users"));
            @JsonSchemaProperty(readOnly = true)
            Integer pageSize) {

        PageRequest pr = userPageAndSorting(page, pageSize);
        Predicate p = userQuery(search, enabled, group, mfaRequired);
        final Page<User> users = userRepository.findAll(p, pr);

        Page<UserListRowResource> resourcePage = users.map(rowResourceAssembler::toModel);
        ResourceList<UserListRowResource> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(UserListController.class).list(search, enabled, group, mfaRequired,  page - 1, pageSize))
                        .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(UserListController.class).list(search, enabled, group, mfaRequired,  page + 1, pageSize))
                        .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pageSize);
        resourceList.setNumItems(resourcePage.getTotalElements());
        addDescribedByLink(resourceList);
        return resourceList;
    }

    @Override
    public ResponseEntity<JsonSchema> describe(WebRequest request) {
        Object invocation = self().list("", true, "", null, 0, 0);

        JsonSchema listRequestParamSchema = getSchemaCreator().createForRequestParams(invocation);

        // Make sure the parameters are valid services and projects by extending the enumeration of them.
        // NB extends[] = allOf, type[] = anyOf (this is more explicit in JSON Schema v4)
        var linkBuilder = linkToApi(methodOn(GroupsController.class).enumerateAllGroups());
        listRequestParamSchema.setExtends(new JsonSchema[] {
                new TypedReferenceSchema(linkBuilder.toUri().toString(),
                        listRequestParamSchema.getType())
        });

        LinkDescriptionObject instancesLink = new SchemaProvidingLinkDescriptionObject()
                .setRel(REL_INSTANCES)
                .setMethod(RequestMethod.GET.toString())
                .setHref(linkTo(invocation).toUriComponentsBuilder().replaceQuery(null).build(false).toString())
                .setSchema(listRequestParamSchema);

        JsonSchema schema = getSchemaCreator().create(UserListRowResource.class,
                self().describe(request),
                Optional.of(instancesLink),
                Optional.empty());

        cacheForXSecs(request, 180);
        return ResponseEntity.ok(schema);
    }

    // NB also see ReportController.reportUsers
    private Predicate userQuery(String search, Boolean enabled, String group, Boolean mfaRequired) {
        BooleanBuilder p = new BooleanBuilder();
        if (enabled) {
            p.and(user.enabled.isTrue());
        }
        if (search != null) {
            p.and(
                    user.contact.lastName.containsIgnoreCase(search)
                            .or(user.contact.firstName.containsIgnoreCase(search))
                            .or(user.username.containsIgnoreCase(search)));
        }
        if (group != null) {
            p.and(user.groupMemberships.any().group.name.equalsIgnoreCase(group));
        }
        if (mfaRequired != null) {
            p.and(user.mfaRequired.eq(mfaRequired));
        }
        return p;
    }

    private PageRequest userPageAndSorting(int page, int pageSize) {

        QSort sort = new QSort(user.username.asc().nullsLast());

        return PageRequest.of(page, pageSize, sort);
    }

}
