package com.ecco.webApi.finance;

import com.ecco.contacts.dao.AddressHistoryRepository;
import com.ecco.dom.contacts.AddressHistory;
import com.ecco.finance.webApi.dto.ClientSalesServiceChargeInvoiceDetailResource;
import com.ecco.repositories.contracts.ContractRepository;
import com.google.common.collect.Range;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Service
@AllArgsConstructor
public class FinanceService {

    public static final int SERVICECHARGE_CONTRACT_ID = 203;

    private final AddressHistoryRepository addressHistoryRepository;
    private final ContractRepository contractRepository;

    public List<ClientSalesServiceChargeInvoiceDetailResource.Line> getChargesByServiceRecipient(Integer serviceRecipientId) {
        List<AddressHistory> history = addressHistoryRepository.findByServiceRecipientIdOrderByValidFromDesc(serviceRecipientId);

        // get the last contract (for testing) of 'service charge' type (regardless of dates), to use its rate cards
        // NB See RateCardCalculation#getRateCardInDate and DemandSchedule#allowableRateCards for how we can choose rateCards for rotas
        var contract = contractRepository.findByContractTypeIdOrderByIdDesc(SERVICECHARGE_CONTRACT_ID)
                .stream().findFirst().orElseThrow();

        Range<Instant> dates = Range.openClosed(Instant.EPOCH, Instant.now());
        FinanceServiceChargeCalculationDefault calc = new FinanceServiceChargeCalculationDefault(dates, history, contract.getRateCards());

        return calc.calculateLines();
    }

    public List<ClientSalesServiceChargeInvoiceDetailResource.Line> getCharges(List<AddressHistory> history, Range<Instant> dates) {
        // get the last contract (for testing) of 'service charge' type (regardless of dates), to use its rate cards
        // NB See RateCardCalculation#getRateCardInDate and DemandSchedule#allowableRateCards for how we can choose rateCards for rotas
        var contract = contractRepository.findByContractTypeIdOrderByIdDesc(SERVICECHARGE_CONTRACT_ID)
                .stream().findFirst().orElseThrow();

        FinanceServiceChargeCalculationDefault calc = new FinanceServiceChargeCalculationDefault(dates, history, contract.getRateCards());
        return calc.calculateLines();
    }

}
