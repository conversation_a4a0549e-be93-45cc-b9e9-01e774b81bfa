package com.ecco.webApi.finance;

import com.ecco.finance.webApi.dto.ClientSalesServiceChargeInvoiceDetailResource;
import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.google.common.collect.Range;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * NB Unused class because opted to use the Line directly, instead
 */
public class ClientSalesServiceChargeInvoiceDetailResourceAssembler {
    private final ClientSalesServiceChargeInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler;

    public ClientSalesServiceChargeInvoiceDetailResourceAssembler(ClientSalesServiceChargeInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler) {
        this.clientSalesInvoiceDetailResourceLineAssembler = clientSalesInvoiceDetailResourceLineAssembler;
    }

    public static List<ClientSalesServiceChargeInvoiceDetailResource> toResourceArray(ClientSalesServiceChargeInvoiceDetailResourceAssembler assembler,
                                                                                      Range<LocalDate> range, int serviceRecipientId, List<AddressHistoryViewModel> addressHistory) {
        // find the charging periods
        var chargeablePeriods = addressHistory.stream()
                .filter(a -> a.buildingId != null)
                .map(a -> assembler.toResource(serviceRecipientId, a.buildingId, a.validFrom.toLocalDate(), a.validTo == null ? null : a.validTo.toLocalDate()));

        // split into the range we are asking for
        //var earliestChargeableDate = chargeablePeriods.min(l -> l.)

        return chargeablePeriods.toList();
    }

    /**
     * Should be working off a ClientSalesInvoice, but we don't have any persisted data, just calculations from address history
     */
    @Nonnull
    private ClientSalesServiceChargeInvoiceDetailResource toResource(int srId, int bldgId, @Nonnull LocalDate from, @Nullable LocalDate to) {
        var line = this.clientSalesInvoiceDetailResourceLineAssembler.toResource(srId, String.valueOf(bldgId), from, to);
        return new ClientSalesServiceChargeInvoiceDetailResource(srId, from, Collections.singletonList(line));
    }

    /*private static LinkBuilder linkToInvoiceDetail(Integer invoiceId) {
        return linkToApi(methodOn(FinanceServiceChargeController.class).fetchSingleInvoice(invoiceId));
    }*/

}
