package com.ecco.webApi.serviceConfig;

import com.ecco.evidence.EvidenceTask;
import com.ecco.infrastructure.util.FlagMap;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.serviceConfig.repositories.ServiceType_OutcomeRepository;
import com.ecco.serviceConfig.repositories.ServiceType_OutcomeThreatRepository;
import com.google.common.base.Predicate;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class TaskDefinitionEntrySettingSpecificOutcomesByIdHandler implements TaskDefinitionEntrySettingSpecificHandler {

    @Nonnull private final OutcomeRepository outcomeRepository;
    @Nonnull private final ServiceType_OutcomeRepository serviceTypeOutcomeRepository;
    @Nonnull private final ServiceType_OutcomeThreatRepository serviceTypeOutcomeThreatRepository;
    @Nonnull private final TaskDefinitionService taskDefinitionService;

    @PersistenceContext
    private EntityManager entityManager;

    public static final String OUTCOMESBYID = "outcomesById";

    @Autowired
    public TaskDefinitionEntrySettingSpecificOutcomesByIdHandler(
            @Nonnull OutcomeRepository outcomeRepository,
            @Nonnull ServiceType_OutcomeRepository serviceTypeOutcomeRepository,
            @Nonnull ServiceType_OutcomeThreatRepository serviceTypeOutcomeThreatRepository,
            @Nonnull TaskDefinitionService taskDefinitionService) {
        this.outcomeRepository = outcomeRepository;
        this.serviceTypeOutcomeRepository = serviceTypeOutcomeRepository;
        this.serviceTypeOutcomeThreatRepository = serviceTypeOutcomeThreatRepository;
        this.taskDefinitionService = taskDefinitionService;
    }


    @Override
    public boolean canHandle(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting) {
        return OUTCOMESBYID.equals(taskDefinitionEntrySetting.getName());
    }

    @Override
    public void applyChanges(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {

        EvidenceTask task = EvidenceTask.fromTaskName(viewModel.taskName);
        var type = taskDefinitionService.getTaskType(task);
        if (taskDefinitionService.isSupportSmartStepBased(type)) {
            applyOutcomeSupports(taskDefinitionEntrySetting, viewModel);
        } else if (taskDefinitionService.isThreatBased(type)) {
            applyOutcomeThreats(taskDefinitionEntrySetting, viewModel);
        } else {
            throw new IllegalArgumentException("Don't know how to handle '" + OUTCOMESBYID + "' for " + viewModel.taskName);
        }
    }

    private void applyOutcomeSupports(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {

        final ImmutableSet<Long> outcomeIdsToAdd = findOutcomeIdsDifferences(
                new FlagMap(taskDefinitionEntrySetting.getValue()), new FlagMap(viewModel.valueChange.to));
        final ImmutableSet<Long> outcomeIdsToRemove = findOutcomeIdsDifferences(
                new FlagMap(viewModel.valueChange.to), new FlagMap(taskDefinitionEntrySetting.getValue()));
        if (outcomeIdsToAdd.isEmpty() && outcomeIdsToRemove.isEmpty()) {
            return;
        }

        long serviceTypeId = taskDefinitionEntrySetting.getServiceType_TaskDefinition().getMultiId().getServiceType().getId();
        ServiceType serviceType = this.entityManager.getReference(ServiceType.class, serviceTypeId);

        final Iterable<OutcomeSupport> outcomesToAdd = findOutcomeSupportsById(outcomeIdsToAdd);
        outcomesToAdd.forEach(outcome -> {
            ServiceType_OutcomeSupport_MultiId stosId = new ServiceType_OutcomeSupport_MultiId(serviceType, outcome);
            ServiceType_OutcomeSupport stos = new ServiceType_OutcomeSupport(stosId);
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeOutcomeRepository.save(stos);
        });
        final Iterable<OutcomeSupport> outcomesToRemove = findOutcomeSupportsById(outcomeIdsToRemove);
        outcomesToRemove.forEach(outcome -> {
            ServiceType_OutcomeSupport_MultiId stosId = new ServiceType_OutcomeSupport_MultiId(serviceType, outcome);
            ServiceType_OutcomeSupport stos = new ServiceType_OutcomeSupport(stosId);
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeOutcomeRepository.delete(stos);
        });
    }

    private void applyOutcomeThreats(ServiceType_TaskDefinitionSetting taskDefinitionEntrySetting, TaskDefinitionEntrySettingCommandViewModel viewModel) {

        final ImmutableSet<Long> outcomeIdsToAdd = findOutcomeIdsDifferences(
                new FlagMap(taskDefinitionEntrySetting.getValue()), new FlagMap(viewModel.valueChange.to));
        final ImmutableSet<Long> outcomeIdsToRemove = findOutcomeIdsDifferences(
                new FlagMap(viewModel.valueChange.to), new FlagMap(taskDefinitionEntrySetting.getValue()));
        if (outcomeIdsToAdd.isEmpty() && outcomeIdsToRemove.isEmpty()) {
            return;
        }

        long serviceTypeId = taskDefinitionEntrySetting.getServiceType_TaskDefinition().getMultiId().getServiceType().getId();
        ServiceType serviceType = this.entityManager.getReference(ServiceType.class, serviceTypeId);

        final Iterable<OutcomeThreat> outcomesToAdd = findOutcomeThreatsById(outcomeIdsToAdd);
        outcomesToAdd.forEach(outcomeThreat -> {
            ServiceType_OutcomeThreat_MultiId stotId = new ServiceType_OutcomeThreat_MultiId(serviceType, outcomeThreat);
            ServiceType_OutcomeThreat stot = new ServiceType_OutcomeThreat(stotId );
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeOutcomeThreatRepository.save(stot);
        });

        final Iterable<OutcomeThreat> outcomesToRemove = findOutcomeThreatsById(outcomeIdsToRemove);
        outcomesToRemove.forEach(outcomeThreat -> {
            ServiceType_OutcomeThreat_MultiId stotId = new ServiceType_OutcomeThreat_MultiId(serviceType, outcomeThreat);
            ServiceType_OutcomeThreat stot = new ServiceType_OutcomeThreat(stotId);
            // NB this doesn't update the servicetype version, but we really don't need to worry about that
            serviceTypeOutcomeThreatRepository.delete(stot);
        });
    }

    private ImmutableSet<Long> findOutcomeIdsDifferences(Map<String, Boolean> from,
                                                         Map<String, Boolean> to) {

        Set<String> outcomesDifferentStr = Sets.difference(to.keySet(), from.keySet()).immutableCopy();
        Set<Long> outcomesDifferentIds = new HashSet<>(outcomesDifferentStr.size());
        outcomesDifferentStr.forEach(i -> outcomesDifferentIds.add(Long.valueOf(i)));
        return ImmutableSet.copyOf(outcomesDifferentIds);
    }

    private Iterable<OutcomeSupport> findOutcomeSupportsById(final ImmutableSet<Long> outcomeIdsToAdd) {

        Iterable<OutcomeSupport> allOutcomeSupports = outcomeRepository.findAllSupport();
        Iterable<OutcomeSupport> filtered = Iterables.filter(allOutcomeSupports, new Predicate<OutcomeSupport>() {
            @Override
            public boolean apply(OutcomeSupport o) {
                return outcomeIdsToAdd.contains(o.getId());
            }
        });

        return filtered;
    }

    private Iterable<OutcomeThreat> findOutcomeThreatsById(final ImmutableSet<Long> outcomeIdsToAdd) {

        Iterable<OutcomeThreat> allOutcomeThreats = outcomeRepository.findAllThreat();
        Iterable<OutcomeThreat> filtered = Iterables.filter(allOutcomeThreats, new Predicate<OutcomeThreat>() {
            @Override
            public boolean apply(OutcomeThreat o) {
                return outcomeIdsToAdd.contains(o.getId());
            }
        });

        return filtered;
    }

}
