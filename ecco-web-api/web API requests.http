
# These are requests you can run with JetBrains IDEA products such as IntelliJ
# NOTE: See also "Rota requests.http"

###
GET http://localhost:8415/test-merge-azure?srId=63


###
POST https://prod-24.uksouth.logic.azure.com/workflows/7f1a9c67381c43a7b95451479e2766a1/triggers/manual/paths/invoke/horton?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=usaEHuBPg2QmtON6upcV66Q3a_D4oNUPx4O9RE20pWw
Content-Type: application/json

{
    "triggerId": "13210887-5eea-4b73-94d8-2a4d1e62d37d",
    "letterTemplateId": "12345",
    "letterData": {
        "referrals_clients.firstname": "First2",
        "referrals_clients.lastname": "Last"
    }
}


###
POST http://localhost:8888/ecco-war/api/reports/referrals/page/1/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{"from":null,"to":"2020-03-25","selectionPropertyPath":null,"referralStatus":"liveAtEnd","optionalData":["referralEvents"]}

###
# Client search (ecco only)
# POST https://demo.eccosolutions.co.uk/domcare/api/clients/local/query
POST http://localhost:8888/ecco-war/api/clients/local/query
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "lastName": null,
  "address": {
    "address": [
      "12 Va"
    ]
  }
}

###
# Client search (ecco only)
POST http://localhost:8888/ecco-war/api/clients/local/query
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "address": {
    "postcode": "VC2"
  }
}

###
# Client search (all sources)
POST https://demo.eccosolutions.co.uk/domcare/api/clients/all/query
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "firstName": "s",
  "lastName": "a",
  "address": {
    "address": ["12 Va"]
  }
}

###
# soft delete request
# but then it's hard to do the real delete - it wants a json view model body
POST https://app.eccosolutions.co.uk/mcrs/api/service-recipient/210/delete-request/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "uuid":"{{$uuid}}",
  "commandUri": "service-recipient/210/delete-request/",
  "timestamp": "2024-12-27T18:00:00.000Z",
  "serviceRecipientId": 210,
  "revoke": false,
  "reason": "z6590",
  "deleteParentIfPossible": true
}

###
# soft delete
POST https://app.eccosolutions.co.uk/mcrs/api/service-recipient/210/delete/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "uuid":"{{$uuid}}",
  "commandUri": "service-recipient/210/delete/",
  "timestamp": "2024-12-27T18:00:00.000Z",
  "serviceRecipientId": 210,
  "requestDeletionUuid": "5bd2a416-85f9-4957-9254-6cc5a6d21c6a",
  "jsonViewModel": "{}",
  "revoke": false,
  "reason": "z6590",
  "deleteParentIfPossible": true
}


###
# rota drop
# see z5764
# use https://www.debugbear.com/basic-auth-header-generator
# get the activitRef from the network tab on deleting a schedule
# find a drop reason - select * from cfg_list_definitions where listname='eventStatusRateId';
POST https://app.eccosolutions.co.uk/guhg/api/rota/workers:-/appointment-action/22153/
Authorization: Basic
Content-Type: application/json

{
    "operation":"drop",
    "uuid":"{{$uuid}}",
    "commandUri":"rota/workers:-/appointment-action/200023/",
    "timestamp":"2024-04-04T12:06:38.207Z",
    "activityRef":"6488f85d-85ed-43eb-9aa8-ae8aea3b7f06:20240601T000000",
    "serviceRecipientId":22153,
    "resourceFilter":"workers:-",
    "demandFilter":"svccats:-",
    "dropReasonId":160,
    "commandName":"rotaApptAction"
}


###
# rota re-instate
# see z5764
# use https://www.debugbear.com/basic-auth-header-generator
# get the activitRef from the network tab on deleting a schedule
# find a drop reason - select * from cfg_list_definitions where listname='eventStatusRateId';
POST https://app.eccosolutions.co.uk/guhg/api/rota/workers:-/appointment-action/22153/
Authorization: Basic
Content-Type: application/json

{
    "operation":"reinstate",
    "uuid":"{{$uuid}}",
    "commandUri":"rota/workers:-/appointment-action/200023/",
    "timestamp":"2024-04-04T12:26:00.000Z",
    "activityRef":"6488f85d-85ed-43eb-9aa8-ae8aea3b7f06:20240601T000000",
    "serviceRecipientId":22153,
    "resourceFilter":"workers:-",
    "demandFilter":"svccats:-",
    "commandName":"rotaApptAction"
}



###
# rota deallocate
# see z5764
POST https://app.eccosolutions.co.uk/guhg/api/rota/workers:-/appointment-recurring-action/22153/
Authorization: Basic
Content-Type: application/json

{
    "operation":"deallocate",
    "uuid":"{{$uuid}}",
    "commandUri":"rota/workers:-/appointment-recurring-action/200023/",
    "timestamp":"2024-04-04T12:35:00.000Z",
    "activityRef":"6488f85d-85ed-43eb-9aa8-ae8aea3b7f06",
    "serviceRecipientId":22153,
    "resourceFilter":"workers:-",
    "demandFilter":"svccats:-",
    "deallocateResourceId":21,
    "partSchedule": {"applicableFromDate":  "2024-04-04"},
    "commandName":"rotaApptActionRec"
}


###
# rota recreate
POST http://localhost:8888/ecco-war/api/rota/-/recreate/1db7a535-4e90-4a24-b699-da973f034ee0/?splitDate=2024-12-12
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json


###
# Attribute change
POST http://localhost:8888/ecco-war/api/service-recipients/200000/attributeChange/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "serviceRecipientId": 200000,
  "uuid": "{{$uuid}}",
  "commandUri":"service-recipients/200000/attributeChange/",
  "timestamp":"2020-05-01T10:10:14.854Z",
  "attributePath": "worker.crbNumber",
  "attributeType": "text",
  "valueChange": {"to": "bob"}
}

###
# clear cache
POST https://app.eccosolutions.co.uk/evolve/api/cache-config/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "uuid": "{{$uuid}}",
  "commandUri":"cache-config/",
  "timestamp":"2025-07-08T10:10:00.000Z",
  "all": true
}


###
POST https://withyou.eccosolutions.co.uk/withyou/api/acls/autoAcls/
Authorization: Basic xxx
Content-Type: application/json

{}

###
GET http://localhost:8888/ecco-war/api/users/sysadmin/
Authorization: Basic c3lzYWRtaW46c3Vubnkx

###
GET http://localhost:8888/ecco-war/api/groups/
Authorization: Basic c3lzYWRtaW46c3Vubnkx

###
GET http://localhost:8888/ecco-war/api/groups$enumSchema/
Authorization: Basic c3lzYWRtaW46c3Vubnkx

###
# This will correctly fail on UAT with 1 UPPER 1 DIGIT complexity set
POST http://localhost:8888/ecco-war/api/users/davidh/updatePassword/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
  "newPassword":"asdfasdfasdf"
}


###
GET {{baseUrl}}/nav/secure/login.html

###
# @no-redirect
#POST http://localhost:8888/nav/secure/j_acegi_security_check
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=sysadmin&j_password=sunny1

###
# @no-redirect
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=FAILME&j_password=NOW

###
# @no-redirect
POST http://localhost:8899/login
Content-Type: application/x-www-form-urlencoded

username=sysadmin&password=sunny1

###
# @no-redirect
POST http://localhost:8899/logout
Content-Type: application/x-www-form-urlencoded

###
GET {{baseUrl}}/users/


###
GET {{baseUrl}}/nav/secure/login.html

###
# @no-redirect
#POST http://localhost:8888/nav/secure/j_acegi_security_check
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=sysadmin&j_password=sunny1

###
# @no-redirect
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=FAILME&j_password=NOW

###
###
POST http://localhost:8899/login
Content-Type: application/x-www-form-urlencoded

username=sysadmin&password=sunny1


###
GET {{baseUrl}}/nav/secure/login.html

###
# @no-redirect
#POST http://localhost:8888/nav/secure/j_acegi_security_check
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=sysadmin&j_password=sunny1

###
# @no-redirect
POST {{baseUrl}}/nav/secure/j_acegi_security_check
Content-Type: application/x-www-form-urlencoded

j_username=FAILME&j_password=NOW

###
###
POST http://localhost:8899/login
Content-Type: application/x-www-form-urlencoded

username=sysadmin&password=sunny1

###
POST http://localhost:8888/ecco-war/api/text-message/200000/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

{
    "serviceRecipientId": 200000,
    "body": "Test message"
}

###
POST {{baseUrl}}/api/contacts/sms/********-0000-babe-babe-dadafee1600d
#Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/x-www-form-urlencoded

AccountSid=ACa2b5b27ff46ba39f330adccf9268f44c&To=%2b447890123456&From=***********&&Body=Working%3F%3F&FromCountry=GB


###
POST https://jigsaw.eccosolutions.co.uk/jigsaw/api/email-alert/tasks/1/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

###
POST http://localhost:8888/ecco-war/api/email-schedule/tasks/1/
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json

###
GET http://localhost:8888/ecco-war/api/p/report/stats/


###
# clear cache
POST http://localhost:8888/ecco-war/nav/secure/admin/clearAclCache.html
Authorization: Basic c3lzYWRtaW46c3Vubnkx
Content-Type: application/json
