package com.ecco.infrastructure.dom;

import java.util.UUID;

import com.ecco.infrastructure.Created;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@Access(AccessType.FIELD)
@JsonIgnoreProperties("commandName") // for where BaseCommand is part of class used as the DTO
public abstract class BaseCommand<KEY extends Number> implements Created {

    @Nullable
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @Column(nullable = false)
    private DateTime created;

    @Nullable
    @Column(name = "uuid", nullable = true, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID uuid;

    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentInstantAsTimestamp")
    @Nonnull
    @Column(name = "commandCreated", nullable = false)
    private Instant remoteCreationTime;

    /**
     * Gets the ID of the user who submitted the command.
     */
    @Getter
    @Column(nullable = false)
    private long userId;

    @Nonnull
    @Lob
    @Column(nullable = false)
    @Basic(fetch = FetchType.LAZY)
    private String body;

    @Getter
    @Setter
    @Column
    private boolean draft;

    protected BaseCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime, long userId, @Nonnull String body) {
        this.uuid = uuid;
        this.remoteCreationTime = remoteCreationTime;
        this.userId = userId;
        this.body = body;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected BaseCommand() {
    }

    /**
     * Gets the time at which this command was applied on the server.
     * <p/>
     * This is not necessarily the same as the time at which the command was
     * created on the client.
     * <p/>
     * For interoperability with other implementations of {@link Created}, this
     * field is somewhat confusingly named.
     *
     * @see #getRemoteCreationTime()
     */
    @Nullable
    @Override
    public DateTime getCreated() {
        return created;
    }

    @Override
    public void setCreated(@Nullable DateTime now) {
        this.created = now;
    }

    abstract public KEY getId();

    /**
     * Gets the UUID uniquely identifying the command.
     * <p/>
     * Used for de-duplication.
     */
    @Nullable
    public UUID getUuid() {
        return this.uuid;
    }

    /**
     * Gets the time and date at which the command was created on the client.
     * <p/>
     * This is not necessarily the same as the time at which the command was
     * applied on the server.
     *
     * @see #getCreated()
     */
    @Nonnull
    public Instant getRemoteCreationTime() {
        return remoteCreationTime;
    }

    /**
     * Gets the body of the command as a JSON string.
     */
    @Nonnull
    public String getBody() {
        return body;
    }

    public String getCommandName() {
        return null; // Override to implement
    }

}
